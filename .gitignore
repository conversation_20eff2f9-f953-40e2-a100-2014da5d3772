# Dependencies
/node_modules

# Production
/build

# Generated files
.docusaurus
.cache-loader

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Generated by Cargo
# will have compiled files and executables
**/target

# These are backup files generated by rustfmt
**/*.rs.bk

/html/
cargo-test-*

**.pyc

qdrant_storage/**
.idea/**