[package]
name = "pianorhythm-docs-bot"
version = "0.1.0"
edition = "2021"
rust-version = "1.86"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
langchain-rust = { version = "4.6.0", features = ["sqlite", "qdrant"] }
serde_json = "1.0.137"
tokio = "1.43.0"
futures-util = "0.3.31"
sqlite-vec = "0.1.6"
rusqlite = { version = "0.31.0", features = ["bundled"] }
walkdir = "2.5.0"
actix = "0.13.5"
actix-web = "4.9.0"
actix-web-actors = "4.3.0"
serde = { version = "1.0.217", features = ["derive"] }
regex = "1.11.1"
log = "0.4.25"
pretty_env_logger = "0.5.0"
expiringmap = "0.1.2"