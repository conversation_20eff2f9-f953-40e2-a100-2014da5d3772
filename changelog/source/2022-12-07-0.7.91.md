---
  date: 2022-12-07
  version: 0.7.91
  tags:
    - 0.7.91
    - changelog
  
---

# 0.7.91

## :rocket: New Features
- **UI Themes** Added themes! This is just the first draft of this feature so there are certain color issues on some themes. They'll be eventually fixed in the coming updates. To change a theme, go to `Settings -> (Graphics) UI -> Theme`

- **Reverb Settings** ([PRFP-988](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-988)) Added customizable reverb settings. To adjust them, go to `Settings -> Soundfont -> Advanced`

- **New stage models!** You can try the new stages by going to `New Room -> Room Stage`.

- **Ear Training Games** These exercises will improve your musical ability by developing a more intuitive understanding of what you hear. Two exercises were added: `Perfect Pitch` and `Scales`. To try them out, go to `New Room -> Room Mode -> SoloGame`.

- **Midi Player** Added a built-in midi player that renders like <PERSON><PERSON><PERSON><PERSON>'s waterfall notes. You can test it out by dragging a midi file in the main window or by going to `Instrument Dock -> Tools -> Open Midi File`.

## :bug: Bug Fixes
- ([PRFP-807](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-807)) Fixed issue with Touhou soundfont only emitting sound to one speaker from the lower notes.
