---
  date: 2023-05-04
  version: 0.8.0
  tags:
    - 0.8.0
    - changelog
  
---

# 0.8.0

## :rocket: New Features

- **Added plugins!** ([PRFP-629](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-629)) Added the first draft of a plugin system to allow users
to build local plugins to extend PianoRhythm. Find out more here: [Plugins Guide](/advanced-guides/plugins)

- **Added Changelog Modal** ([PRFP-1022](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1022)) A modal of the latest changes will now show up after a new version update.

- **Added User Velocity Percentage Slider** ([PRFP-1045](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1045)) Added a global slider alongside individual sliders for controlling the total percentage value of other users' velocity inputs. You can find the global setting in `Settings -> Midi` and find individual sliders by clicking on user profiles in the sidebar.

## :ok_hand: Changes
- **Updated Desktop App (Tauri -> v1.3.0)** If you would like to know the technical details, you
can check out their blog post: [Tauri 1.3.0](https://tauri.app/blog/2023/05/03/tauri-1-3)
- **Updated Graphics Engine (Bablyon.js -> v6.0)** ([PRFP-1024](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1024)) Find out more here [Babylon.js v6.0](https://babylonjs.medium.com/announcing-babylon-js-6-0-dcb5f1662e3a)
- ([PRFP-1035](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1035)) (Development) Updated Vite to 4.
- ([PRFP-1033](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1033)) (Development) Added some more much needed unit tests.
- ([PRFP-1033](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1033)) (Development) Added some much needed E2E (Cypress) tests.
- ([PRFP-1055](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1055)) Added an option to experiment with WebGPU rendering.
- ([PRFP-1043](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1043)) Added a visual indication when a user has a different soundfont loaded than you do.
- (Development) Minor internal refactors.

## :bug: Bug Fixes
- ([PRFP-1021](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1021)) Fixed an issue with other players not being able to hear your drums.
- ([PRFP-1029](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1029)) Fixed a glitch with the room owner's crown display.
- ([PRFP-1034](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1034)) Fixed issue with executing unit tests.
- ([PRFP-1030](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1030)) Fixed issue with the reverb modal and other related modals, due to the UI sliders (noUiSlider) being broken.
- ([PRFP-1032](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1032)) Fixed glitch with the `New Messages` button sometimes showing up inappropriately.
- ([PRFP-1036](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1036)) Fixed an issue with trying to display the latest changelog from the docs page.
- ([PRFP-1039](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1039)) Fixed multi channel slot mode not emitting properly in the desktop app.
- Minor internal bug fixes.

<!----------------------------------------------->
