---
  date: 2023-07-17
  version: 0.8.30
  tags:
    - 0.8.30
    - changelog
  
---

# 0.8.30

## :rocket: New Features
- `Version: 0.8.37`
  - **Added a help chat bot!** ([PRFP-1089](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1089)) The first draft of the PianoRhythm Help Bot is now here. This was just a fun little thing to work on. You can ask it general questions about PianoRhythm. It's primary source is from the documentation site but you can also ask it some questions about the real time state of the server, such as how many players are online or the total number of registered users. To interact with it, just type in `@helpbot (insert question here)` in the chat bar.

:::note
  You must at least be a registered member to use the help bot. So create an account, today!
:::

- `Version: 0.8.34`
  -  **Added build pipelines for MacOS and Linux** ([PRFP-1079](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1079)) Added desktop builds for MacOS and Linux. *Will need extensive testing*.
  -  **Added customizable keyboard layout** ([PRFP-1080](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1080)) For those keyboard warriors out there, you can now
  individually map each key on your keyboard to a corresponding note on the piano. You can find this at: `Settings -> Input -> (Piano Keys Layout) Custom`. Once selected, click on the `Customize Layout Keys` button.

- `Version: 0.8.33`
  - **Midi Step Sequencer** ([PRFP-502](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-502)) Added a midi/drum step sequencer. Find out more here: [Midi Step Sequencer](/guides/sequencer/)

- `Version: 0.8.30`
  - **Orchestra Mode!** ([PRFP-1026](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1026)) Added the first draft of orchestra mode. Find out more here: [Orchestra Mode](/guides/orchestra-mode/)
  - **Piano Customization!** ([PRFP-1073](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1073)) Find out more here: [Piano Customization](/guides/customization/customization-piano)
  - **Channel Parameter Sliders** ([PRFP-1070](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1070)) Added functionality to individually set the volume and pan values for channels. Find out more here: [Channel Parameters](/guides/instrument-dock/channel-parameters)
  - **Increased Max Players Limit** ([PRFP-1072](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1072)) Increased max players to 20 for normal rooms and 30 for lobbies.
  - **Added setting to set max channels for Multi Mode** ([PRFP-1074](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1074)) You can now set the max number of channels to use during multi mode. You can find this setting by going to: `Settings -> Midi -> Max Multi Mode Channels`

## :smile: Enhancements
- `Version: 0.8.34`
  - ([PRFP-1084](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1084)) Increase default room size room to 20 in UI.
  - ([PRFP-1081](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1081)) Add functionality for canvas to use main thread when offscreen canvas is not supported. You can also manually toggle this in the settings by going to: `Settings -> Graphics -> Enable Offscreen Canvas`
- `Version: 0.8.30`
  - ([PRFP-1071](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1071)) Volume button in the bottom bar is now highlighted when the global volume is muted or zero.
  - ([PRFP-1066](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1066)) Added the user slot mode to the meta details in the mini profile card.

## :bug: Bug Fixes
- `Version: 0.8.38`
  - Simply fixed an issue with the desktop builds not loading properly.
- `Version: 0.8.35`
  - ([PRFP-1075](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1075)) Fixed: Audio engine doesn't work when logging out and relogging back in, without refreshing page
- `Version: 0.8.34`
  - ([PRFP-1083](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1083)) Fixed an issue where notes from other users were not being emitted to midi outputs.
  - ([PRFP-1085](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1085)) Fixed issue with MIDI player notes showing up behind the keys and going through the piano model.
- `Version: 0.8.32`
  - ([PRFP-1076](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1076)) PRFP-1076 Can't see other player notes playing on keys?
  - ([PRFP-1077](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1077)) Instruments list not updating when switching soundfonts.
  - ([PRFP-1078](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1078)) Keys with no sound not being represented properly.
- `Version: 0.8.30`
  - ([PRFP-1069](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1069)) Fixed an issue where turning on stage effects would remove the room's password.

### General Notes
- `Version: 0.8.30`
  - Avatars (and the piano bench) have been disabled temporarily while I rework a more optimal system.
  - Figured out how to use stereo audio (_it was apparently mono before_) for the web version.
  - ([PRFP-1075](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1075)) There is a known issue about the audio engine not working when relogging in. Working on it!

<!----------------------------------------------->
