---
  date: 2023-09-29
  version: 0.8.46
  tags:
    - 0.8.46
    - changelog
  
---

# 0.8.46

## :rocket: New Features
  - **Added Self Hosting Rooms!** ([PRFP-1149](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1149)) Added the ability to host your own rooms! Find out more here: [Self Hosting Rooms](/tutorials/tutorial-self-host-rooms/)

## :smile: Enhancements
  - ([PRFP-1158](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1158)) Room creation settings are now saved locally so you don't have to keep redoing the inputs when creating a room. So this means that when you reload the page and you were the room owner, then it'll be created with the same settings (including password). _(note: the room password is also saved as plain text in the object in the local storage so I don't recommend using a password you using for other accounts)_
  - ([PRFP-1166](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1166)) Gave good ol' @Near#80366e the `V2 OG MEMBER` badge that he's been waiting for, since forever. :)
  - ([PRFP-1155](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1155)) Added a lobby just for PRO subscribers.
  - To help with server bandwidth, if you're by yourself in a room, then no midi data will be emitted to the server (why didn't I think of this before?). Therefore, regardless of the room settings, note quota will not be a factor.

## :bug: Bug Fixes
  - ([PRFP-1169](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1169)) Fixed issue with logging in by email.
  - ([PRFP-1159](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1159)) Fixed an issue with Orchestra mode rooms (such as the lobby) not showing other player pianos.
  - ([PRFP-1157](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1157)) Fixed a UI issues where the lock icon was not immediately shown when muting a user.
  - ([PRFP-1160](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1160)) Fixed user meta details (on the mini profile when you hover over a user) not immediately showing.
  - ([PRFP-1167](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1167)) Fixed an issue where the app would get "stuck" when trying to enter a room with password in a new session. An "enter room password" modal should now show up.

<!----------------------------------------------->
