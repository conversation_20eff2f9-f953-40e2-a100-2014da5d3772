---
  date: 2023-12-31
  version: 0.9.0
  tags:
    - 0.9.0
    - changelog
  
---

# 0.9.0

You can find out more about this update here in the blog: [Server Upgrade](/blog/update_2023/)

## Versions
  - Notes:
    - If you're encountering any issues when creating a new room, try clicking the "Reset" button in the room creation settings. (Credit: @sun queen#61facd)

  - `Version: 0.9.8` (2024-01-10)
    - ## :rocket: New Features
      - ([PRFP-1197](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1197)) Added two new lobbies: Forest and Studio! [Forest Stage](https://i.gyazo.com/ec010869e6adfb956342cb8d18f31f2a.gif)
      - ([PRFP-1198](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1198)) Added volume slider for stage audio effects.
    - ## :smile: Enhancements
      - ([PRFP-1196](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1196)) Upgrade Tauri (Desktop client) to v1.5.4
    - ## :bug: Bug Fixes
      - ([PRFP-1199](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1199)) Fixed a potential issue with Discord login causing a failure to load on desktop.

  - `Version: 0.9.7` (2024-01-07)
    - ## :bug: Bug Fixes
      - ([PRFP-1193](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1193)) Fixed issue with chat history deletion in normal rooms.
      - ([PRFP-1195](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1195)) Fixed issue with clearing profile description not working.
      - ([PRFP-1191](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1191)) Fixed issue with new Discord users not automatically registering a new account.
      - ([PRFP-1190](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1190)) Fixed issue with input for `edit_badges` command.
      - ([PRFP-1194](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1194)) Fixed `/clear_chat` command for mods.
    - ## :smile: Enhancements
      - ([PRFP-1186](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1186)) All active instruments are shown in the mini user profile.


  - `Version: 0.9.6` (2024-01-07)
    - Note: _You have may have to fully clear your cookies and relogin, if you're having issues logging in with this update._
    - ## :bug: Bug Fixes
      - ([PRFP-1188](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1188)) Fixed certain commands not using the full username.
    - ## :smile: Enhancements
      - ([PRFP-1187](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1187)) Added a new role: Trial Moderator.

  - `Version: 0.9.5` (2024-01-06)
    - ## :bug: Bug Fixes
      - ([PRFP-1182](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1182)) Sheet music repo service should be back up now. Note: _You have may have to clear your cookies and relogin again if you get an error trying to access it._
      - ([PRFP-1183](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1183)) (Server) Lobby chat messages should now persist after deployments.

  - `Version: 0.9.3` (2024-01-02)
    - Minor bug fixes for the front and backend.

  - `Version: 0.9.1`
    - ([PRFP-1179](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1179)) Sound fx not working on initial load.
    - ([PRFP-1177](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1177)) Users can't join password protected rooms.


<!----------------------------------------------->
