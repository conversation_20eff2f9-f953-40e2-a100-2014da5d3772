server {
  listen 80;
  listen [::]:80;

  server_name docs.pianorhythm.io;
  server_tokens off;

  location /.well-known/acme-challenge/ {
    root /var/www/certbot;
  }
}

server {
  # Listen to port 443 on both IPv4 and IPv6.
  listen 443 ssl default_server reuseport;
  listen [::]:443 ssl default_server reuseport;

  server_name docs.pianorhythm.io;

  # Load the certificate files.
  ssl_certificate         /etc/letsencrypt/live/docs-pianorhythm/fullchain.pem;
  ssl_certificate_key     /etc/letsencrypt/live/docs-pianorhythm/privkey.pem;
  ssl_trusted_certificate /etc/letsencrypt/live/docs-pianorhythm/chain.pem;

  # Load the Diffie-<PERSON>man parameter.
  ssl_dhparam /etc/letsencrypt/dhparams/dhparam.pem;

  return 200 'Let\'s Encrypt certificate successfully installed!';
  add_header Content-Type text/plain;
}
