---
title: Looper
id: looper
keywords: [looper, guide]
path: ['/guides/components/looper']
tags:
  - looper
  - guide
---

import BaseScreenShot from '@site/src/components/BaseScreenShot';

## Introduction

The Looper component is a powerful tool that allows you to record and playback data in real-time.
It is a great tool for creating loops, practicing, and experimenting with different ideas.

<BaseScreenShot src={"/img/guide/components/ffe2fced7ce4177c6844c7fcad2e2b8e.gif"} />

## How to get started?

You can find the looper component by going to the `Common Actions` menu and selecting `Looper`:

<BaseScreenShot src={"/img/guide/components/81e92b573e03729315334bd92567a041.gif"} />

## How to use it?

The Looper component has the following features:
- **Record**: Start recording data.
  - `Left click on a track to record.`
- **Play**: Start playing the recorded data.
  - `Press the Play button to play all tracks. You can also toggle playing individual tracks by right clicking on them.`
- **Stop**: Stop the recording or playback.
  - `Press the Stop button to stop all tracks.`
- **Clear**: Clear the recorded data.
  - `Press the Clear button to clear all tracks or the press the icon underneath the track to clear it.`
- **Add/Remove Tracks**: Add or remove tracks. A maximum of 16 tracks can be added.
  - `Press the Add Track button to add a new track. Press the Remove Track button to remove a track.`
- **Auto Trim**: Automatically trim the recorded data.
  - `Press the Auto Trim button to automatically trim the recorded data.`
- **Help**: Get help on how to use the Looper component.
  - `Press the Help button to get help on how to use the Looper component.`

<BaseScreenShot src={"/img/guide/components/22b1e11539f32166f37576eecb2db202.gif"} />

### Color Coding

The Looper component uses color coding to help you identify the different states of the tracks:
_(Using the default theme colors)_

- <div style={{color:"#ef9e08", fontWeight: "bold"}}>Recording</div> The track is currently recording.
- <div style={{color:"#54fad9", fontWeight: "bold"}}>Playing</div> The track is currently playing.
- <div style={{color:"#ecedee", fontWeight: "bold"}}>Stopped</div> The track is currently stopped but has recorded data.
- <div style={{color:"#787f85", fontWeight: "bold"}}>Empty</div> The track is empty.