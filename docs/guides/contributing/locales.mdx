---
title: Locales Guide
id: locales
keywords: [locales, contributing, guide]
path: ['/guides/contributing/locales']
tags:
  - locales
  - contributing
  - guide
---

import BaseScreenShot from '@site/src/components/BaseScreenShot';

## Introduction

PianoRhythm is a multilingual application that supports multiple languages.
This guide will show you how to contribute to the localization of PianoRhythm.
<br/>
[Github Repo](https://github.com/PianoRhythm/pianorhythm-locales)

## How to get started?

You have two primary ways to contribute to the localization of PianoRhythm:

### Forking the Repository

The first way is to fork the repository and create a new locale file or update one in the `locales` directory.
You can copy or use the translation files in the `locales/en` directory as your reference to translate the strings to your desired language.
The README file in the root directory will provide you with more information.
Once you have completed the translation, you can submit a pull request to the main repository.

You can find the link to the repository [here](https://github.com/PianoRhythm/pianorhythm-locales).

<BaseScreenShot src={"/img/contributing/459f2dad5501120110ef34225971800a.png"} />

### Using Fink by Inlang

The other way is to use [Fink by <PERSON>lang](https://fink.inlang.com/), a web-based translation platform that allows you to translate the strings in PianoRhythm.
With your github account, you can sign in to Fink and start translating through their web interface.

<BaseScreenShot src={"/img/contributing/c90008d25f2dfacc4312cc490016e119.png"} />

A fork of the repository will be created for you to work on the translations in your own personal Github account.
Once you have completed the translation, you can submit a pull request to the main repository.

<BaseScreenShot src={"/img/contributing/c0e11c1031ac673b6ff6522be1016703.png"} />

You can find the link to PianoRhythm's Fink by Inlang [here](https://fink.inlang.com/github.com/PianoRhythm/pianorhythm-locales?project=%2Fproject.inlang&lang=en).