---
title: MIDI Player
id: midi-player
keywords: [midi player, midi]
path: ['/guides/midi-player']
tags:
  - midi
  - guide
---

import BaseScreenShot from '@site/src/components/BaseScreenShot';

## Introduction

The MIDI player is a feature that allows you to play MIDI files in PianoRhythm.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/02a5e1fd9c48a044d0c29acf95895d92.gif"} />

## How to get started?

You can either drag and drop a MIDI file into the PianoRhythm window or click on the "Open MIDI File" button by going to `Tools` in the instrument dock.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/12c50d81163e665b887377d623a6ee4f.gif"} />

## Lyrics

The MIDI player also supports lyrics. You can add lyrics to the MIDI file and they will be displayed as the song is played.
Lyric text in a MIDI file simply identifies a chronological sequence of syllables that should be sung with the song, and the time at which each syllable should start to be sung
Each syllable will be animated to show the current syllable being sung.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/1cb6b9400816e5561998debb34680be7.gif"} />
