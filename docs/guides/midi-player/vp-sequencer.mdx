---
title: Virtual Piano Player
id: vp-sequencer
keywords: [virtual piano sequencer, sequencer]
path: ['/guides/vp-sequencer']
tags:
  - sequencer
  - guide
---

import BaseScreenShot from '@site/src/components/BaseScreenShot';

## Introduction

There is now support for a basic Virtual Piano sequencer in PianoRhythm.
This feature allows you to play notes from virtual piano sheets and have them played back in sequence with sound.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/461e41788b0be39c6df1407996ec3419.gif"} />

## How to get started?

There are a few ways to start use the Virtual Piano sequencer:

### Sheet Music Viewer
You can open the `Sheet Music Viewer` and either manually type in notes or paste notes from a virtual piano sheet
after clicking `Create New`. You can also click on `Browse File` to open a virtual piano sheet file from your computer.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/d50c6bf6b1063cbde09c9cc1558422aa.png"} />

After you have entered notes, you can click on the `VP` button at the top to active Virtual Piano mode.
You'll see additional controls for the sequencer such as `Play`, `Pause`, etc. You'll also see the buttons
to change the tempo, toggle auto-scroll, and toggle the sustain.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/055b3f063b3a09c8de2f648d45fe018d.gif"} />

### Sheet Music Repo

You can also use the sequencer when viewing sheet music from the `Sheet Music Repo`. Those that support
the Virtual Piano player will have the category set as `Virtual Piano`.

<BaseScreenShot version={"0.9.9"} src={"/img/guide/midi-player/3625979c5a84fc3ea1ac4bbb2b20bf54.png"} />

