---
title: Orchestra Mode
id: orchestra-mode
keywords: [orchestra mode, orchestra]
path: ['/guides/orchestra-mode']
tags:
  - orchestra
  - guide
---

import BaseScreenShot from '@site/src/components/BaseScreenShot';

## Introduction

Orchestra mode is a room mode where instrument models are shown for most users.
Now, you'll see other players playing on their respective piano, instead of your own. Also, this is
currently the only mode where other users can see your [customized piano](/guides/customization/customization-piano).

Each piano will have their user's username tag floating above it. When that user is playing, you'll only see _their_ piano keys being animated for that respective user.

<BaseScreenShot version={"0.8.3"} src={"/img/guide/orchestra-mode/4466b73548929f1e2a81eda2519d0cbb.png"} />

To enter Orchestra mode, just create a new room and change the room mode to `Orchestra`.
:::note
Guests are not allowed to set room modes when creating rooms. So, create a free account and become a member ASAP!
:::
<BaseScreenShot version={"0.8.3"} src={"/img/guide/orchestra-mode/b97b704cafc18bac6fd0ce0beeb96dcf.png"} />

:::note
There is currently an arbitrary limit of 10 max 3D models that are shown for the current users.
Meaning that if the room currently has 15 users in there, the client will only show piano models
for the first 10.
:::

:::note
To enable/disable displaying other models in Orchestra mode, you can go to `Settings -> Graphics (General) -> Enable Orchestra Models`
:::