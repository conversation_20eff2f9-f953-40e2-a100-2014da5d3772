{"name": "pianorhythm-docs", "version": "0.0.1", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start --port 4000 --hot-only", "build": "docusaurus build --out-dir ../public/docs/", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "parse": "docusaurus parse", "glossary": "docusaurus glossary", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus-terminology/parser": "^1.3.0", "@docusaurus-terminology/term": "^1.0.0", "@docusaurus/core": "3.7.0", "@docusaurus/plugin-client-redirects": "3.7.0", "@docusaurus/plugin-content-docs": "3.7.0", "@docusaurus/plugin-google-gtag": "3.7.0", "@docusaurus/plugin-pwa": "3.7.0", "@docusaurus/preset-classic": "3.7.0", "@docusaurus/theme-classic": "3.7.0", "@docusaurus/theme-common": "3.7.0", "@docusaurus/theme-live-codeblock": "3.7.0", "@docusaurus/theme-mermaid": "^3.7.0", "@docusaurus/utils": "3.7.0", "@docusaurus/utils-common": "3.7.0", "@grnet/docusaurus-glossary-view": "1.0.0-rc.2", "@grnet/docusaurus-term-preview": "1.0.0-rc.1", "@grnet/docusaurus-terminology": "1.0.0-rc.2", "@mdx-js/react": "^3.0.0", "clsx": "^1.1.1", "docusaurus-lunr-search": "3.3.2", "prism-react-renderer": "^2.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-photo-album": "^3.0.2", "semver-sort": "^1.0.0", "styled-components": "6.1.8", "yet-another-react-lightbox": "^3.19.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 3 safari version"]}, "engines": {"node": ">=18.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/tsconfig": "3.7.0", "@docusaurus/types": "^3.7.0", "@types/react": "^19.0.0", "acorn": "^8.7.0", "autocomplete.js": "^0.38.1", "autoprefixer": "^10.4.16", "classnames": "^2.3.1", "cross-env": "^7.0.3", "docusaurus-plugin-typedoc": "^0.19.2", "hogan.js": "^3.0.2", "lunr": "^2.3.9", "postcss": "^8.4.31", "raw-loader": "^4.0.2", "react-is": "^17.0.2", "tailwindcss": "^3.3.3", "typescript": "~5.6.2"}}