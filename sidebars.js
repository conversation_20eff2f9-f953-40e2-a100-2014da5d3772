// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
module.exports = {
  docsSideBar: [
    {
      type: 'category',
      label: 'Guides',
      items: [
        {
          type: 'category',
          label: 'General',
          link: { type: 'doc', id: 'guides/guide' },
          items: [
            { type: 'autogenerated', dirName: 'guides/general' }
          ]
        },
        {
          type: 'category',
          label: 'Rooms',
          link: { type: 'doc', id: 'guides/rooms' },
          items: [
            { type: 'autogenerated', dirName: 'guides/rooms' }
          ]
        },
        {
          type: 'category',
          label: 'Instrument Dock',
          link: { type: 'doc', id: 'guides/instrument-dock/instrument-dock' },
          items: [
            'guides/instrument-dock/channel-parameters'
          ],
        },
        'guides/sheet-music/sheetmusic',
        {
          type: 'category',
          label: 'MIDI Player',
          link: { type: 'doc', id: 'guides/midi-player/midi-player' },
          items: [
            'guides/midi-player/vp-sequencer',
          ]
        },
        // 'guides/sequencer/midi-step-sequencer',
        {
          type: 'category',
          label: 'Audio Components',
          // link: { type: 'doc', id: 'guides/components' },
          items: [
            { type: 'autogenerated', dirName: 'guides/components' }
          ]
        },
        {
          type: 'category',
          label: 'Orchestra Mode',
          link: { type: 'doc', id: 'guides/orchestra-mode/orchestra-mode' },
          items: [
            'guides/customization/customization-piano'
          ]
        },
        'guides/subscription/pro-subscription',
      ],
    },
    {
      type: 'category',
      label: 'Tutorials',
      items: [
        { type: 'autogenerated', dirName: 'tutorials' },
      ],
    },
    {
      type: 'category',
      label: 'Advanced Tutorials',
      items: [
        'advanced-guides/plugins/plugins',
        // {
        //   type: 'category',
        //   label: 'Plugin API',
        //   items: [
        //     "api/modules",
        //     {
        //       type: 'category',
        //       label: 'Classes',
        //       items: [{ type: 'autogenerated', dirName: 'api/classes' }]
        //     }
        //   ]
        // }
      ],
    },
    {
      type: 'category',
      label: 'Troubleshooting',
      items: [
        { type: 'autogenerated', dirName: 'troubleshoot' }
      ]
    },
    {
      type: 'category',
      label: 'Contributing',
      items: [
        { type: 'autogenerated', dirName: 'guides/contributing' },
      ],
    },
  ],
  communitySidebar: [
    { type: 'autogenerated', dirName: 'community' },
    // {
    //   type: 'link',
    //   label: 'Changelog',
    //   href: '/changelog',
    // },
  ],
  developmentSidebar: [
    { type: 'autogenerated', dirName: 'development' },
  ],
};