/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.authorCol {
  max-width: inherit !important;
  flex-grow: 1 !important;
}

.imageOnlyAuthorRow {
  display: flex;
  flex-flow: row wrap;
}

.imageOnlyAuthorCol {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}

.imageOnlyAuthorCol [class^='image'] {
  background-color: var(--ifm-color-emphasis-100);
}

.toggleButton {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
  border-radius: 50%;
  width: var(--ifm-avatar-photo-size);
  height: var(--ifm-avatar-photo-size);
  background-color: var(--ifm-color-emphasis-100);
}

.toggleButtonIconExpanded {
  transform: rotate(180deg);
}
