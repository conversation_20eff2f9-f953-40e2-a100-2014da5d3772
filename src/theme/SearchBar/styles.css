/**
 * Copyright (c) 2017-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.search-icon {
  background-image: var(--ifm-navbar-search-input-icon);
  height: auto;
  width: 24px;
  cursor: pointer;
  padding: 8px;
  line-height: 32px;
  background-repeat: no-repeat;
  background-position: center;
  display: none;
}

.search-icon-hidden {
  visibility: hidden;
}

@media (max-width: 360px) {
  .search-bar {
    width: 0 !important;
    background: none !important;
    padding: 0 !important;
    transition: none !important;
  }

  .search-bar-expanded {
    width: 9rem !important;
  }

  .search-icon {
    display: inline;
    vertical-align: sub;
  }
}
