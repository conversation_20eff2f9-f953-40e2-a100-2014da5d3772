# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 5
  cacheKey: 8

"@algolia/autocomplete-core@npm:1.5.0":
  version: 1.5.0
  resolution: "@algolia/autocomplete-core@npm:1.5.0"
  dependencies:
    "@algolia/autocomplete-shared": 1.5.0
  checksum: e00811f125b271f6f8fbf265251c305fe7d6d7ebdf78766b35b7281bfb65c31139bef890a50f1807c4f97bf16d95c455f6e99db02b42655fe61ff302b0f6e45a
  languageName: node
  linkType: hard

"@algolia/autocomplete-preset-algolia@npm:1.5.0":
  version: 1.5.0
  resolution: "@algolia/autocomplete-preset-algolia@npm:1.5.0"
  dependencies:
    "@algolia/autocomplete-shared": 1.5.0
  peerDependencies:
    "@algolia/client-search": ^4.9.1
    algoliasearch: ^4.9.1
  checksum: b612c50f1e2ec65d182c99ed3182480772214bf4c9263a59ffcf343c2abf24a7d1de597fb03c4ecc8790a39e261a3fb3172bff502476695c7af7b270104dc89e
  languageName: node
  linkType: hard

"@algolia/autocomplete-shared@npm:1.5.0":
  version: 1.5.0
  resolution: "@algolia/autocomplete-shared@npm:1.5.0"
  checksum: b59a9172079dc028aa6f4fd3fc8728e5ced6311ad6e868eafade799e530ae2cd8a65dd1b46d5f876fab231fc792701dccc53684771f955947da938c2ae9ff1ca
  languageName: node
  linkType: hard

"@algolia/cache-browser-local-storage@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/cache-browser-local-storage@npm:4.12.0"
  dependencies:
    "@algolia/cache-common": 4.12.0
  checksum: 865a747c7dac80b446c0cd8d7e0ffcef0ecd92c47488fe0e0bf9da20a5c7a8088e34d3176f35376738dea3707051696c48e551965e6a0164c1133670dff3c165
  languageName: node
  linkType: hard

"@algolia/cache-common@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/cache-common@npm:4.12.0"
  checksum: 3d52a57ff398f7a9137a8c9a1c576a806c7b1b7b122581f61860f36a2ecd0cea76c81d9cf99e3b5848485e8b46a0b65431e97e705208f10341bacb46e0174b98
  languageName: node
  linkType: hard

"@algolia/cache-in-memory@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/cache-in-memory@npm:4.12.0"
  dependencies:
    "@algolia/cache-common": 4.12.0
  checksum: 817b99314bee532ed7d99b3de8125e21816fc7149239568d46a02c37fed4b164e012dbbff4f3dffa066734f48ce6e0b34c58f6dc3580e6e580f703f13d2bd67e
  languageName: node
  linkType: hard

"@algolia/client-account@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/client-account@npm:4.12.0"
  dependencies:
    "@algolia/client-common": 4.12.0
    "@algolia/client-search": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: b2c69a4f376c449ab803173a47a0a7d50e24131892938cf8100266a07394c9a5f11dd359906c9c527ebb7ef3deebe55cee0244dacadf183e2f00fca9feb4aae7
  languageName: node
  linkType: hard

"@algolia/client-analytics@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/client-analytics@npm:4.12.0"
  dependencies:
    "@algolia/client-common": 4.12.0
    "@algolia/client-search": 4.12.0
    "@algolia/requester-common": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: 172b4d048c9febdb7b48619500f8a4d875ffe65f573f9d20af2dc187551553b8384f63582e714c01ab9fe02af1e1bf3e70a556b29b19392e00c19b6fdce0813f
  languageName: node
  linkType: hard

"@algolia/client-common@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/client-common@npm:4.12.0"
  dependencies:
    "@algolia/requester-common": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: dee730abd94f57e73b1c2a921405927b0dc2c5e61978dac58df758fa6d64238b85866458efffe925c7461a543f6d3e5faef92f3cd07237f4f44d027d30305bc9
  languageName: node
  linkType: hard

"@algolia/client-personalization@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/client-personalization@npm:4.12.0"
  dependencies:
    "@algolia/client-common": 4.12.0
    "@algolia/requester-common": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: 1282384826bae8b23fa12b42251d20ebfb8ee416bbcdb7b08494b5c036c2ee5b3e6b9b814f8018d70f6140e4407ac7398ea30f125bb4e5cb338caf8696af688f
  languageName: node
  linkType: hard

"@algolia/client-search@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/client-search@npm:4.12.0"
  dependencies:
    "@algolia/client-common": 4.12.0
    "@algolia/requester-common": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: f059bf5b92e1e6a25b2b8988c1440b441542cde850c941b596c98c4cbf7eadc83358827381bc168564e6332ba7cb5ae4aa54d17759744a8f2ff376e39b1244d0
  languageName: node
  linkType: hard

"@algolia/events@npm:^4.0.1":
  version: 4.0.1
  resolution: "@algolia/events@npm:4.0.1"
  checksum: 4f63943f4554cfcfed91d8b8c009a49dca192b81056d8c75e532796f64828cd69899852013e81ff3fff07030df8782b9b95c19a3da0845786bdfe22af42442c2
  languageName: node
  linkType: hard

"@algolia/logger-common@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/logger-common@npm:4.12.0"
  checksum: 37f969db497b2e5108eea2873b8d3881345c31fb9db351dc6bd29b62222fe2320afed3dc7b48e6a1339ba055ea17da62f645f52b6012e25a6cf6ab38bd052833
  languageName: node
  linkType: hard

"@algolia/logger-console@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/logger-console@npm:4.12.0"
  dependencies:
    "@algolia/logger-common": 4.12.0
  checksum: 7d3af12cccc23dd97df72274ddd8ab5ba7e0c111ffd263880f401c0573c486062cb5cd2f55bab27c61b95188ea2d1b2920195417e2b249576275f22b56b56684
  languageName: node
  linkType: hard

"@algolia/requester-browser-xhr@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/requester-browser-xhr@npm:4.12.0"
  dependencies:
    "@algolia/requester-common": 4.12.0
  checksum: ddda49b50811e4abb75ad66bed56fb232208e9f59070ca24d875b4764ac2618b1308b35606280ae90f604cb1534f8b48a379fd507bb772b981448a74bc36e48c
  languageName: node
  linkType: hard

"@algolia/requester-common@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/requester-common@npm:4.12.0"
  checksum: f1fe357479a10894d3eb3f2c3aa500a6fce5feaca9b6ff9bb57fb61ddde16901a7e7cc8444c2da80d3a90199cf318adbe0f0735a39fb5d24633abb805ce09c3a
  languageName: node
  linkType: hard

"@algolia/requester-node-http@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/requester-node-http@npm:4.12.0"
  dependencies:
    "@algolia/requester-common": 4.12.0
  checksum: 034f38da4a8cf1dbf52f5eb4609bbea61ff29594347da62b523b998f6ae8a400141cd914ad2a9f28f9604090b2bce7a367371ad621eec80bff8f4bf6dc3b0d66
  languageName: node
  linkType: hard

"@algolia/transporter@npm:4.12.0":
  version: 4.12.0
  resolution: "@algolia/transporter@npm:4.12.0"
  dependencies:
    "@algolia/cache-common": 4.12.0
    "@algolia/logger-common": 4.12.0
    "@algolia/requester-common": 4.12.0
  checksum: 025cef43fb3b13bd2ab2caac33fb61739600e7daa5664e1abdfc0fb9a992dcc3110fd356e3481ad9a308a3332cda8c1cb37448697bd74c11b21e0c149f58dede
  languageName: node
  linkType: hard

"@apideck/better-ajv-errors@npm:^0.3.1":
  version: 0.3.2
  resolution: "@apideck/better-ajv-errors@npm:0.3.2"
  dependencies:
    json-schema: ^0.4.0
    jsonpointer: ^5.0.0
    leven: ^3.1.0
  peerDependencies:
    ajv: ">=8"
  checksum: 714e4f89d55d6c48fed0cf68b6a1cf313ee2cdf600908766b6f469d76523f71ad8b55d649cfde776002f6242db44d552176bae2565a3fa2404ebf7030e0b76da
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.10.4, @babel/code-frame@npm:^7.16.7, @babel/code-frame@npm:^7.8.3":
  version: 7.16.7
  resolution: "@babel/code-frame@npm:7.16.7"
  dependencies:
    "@babel/highlight": ^7.16.7
  checksum: db2f7faa31bc2c9cf63197b481b30ea57147a5fc1a6fab60e5d6c02cdfbf6de8e17b5121f99917b3dabb5eeb572da078312e70697415940383efc140d4e0808b
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.13.11, @babel/compat-data@npm:^7.16.4, @babel/compat-data@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/compat-data@npm:7.16.8"
  checksum: 10da2dac5ea9589c251412b00920889910e476c1ab24cd7095577635bc3a27c785151c89db4e26285fd39f509510ec29ab9d7e721f4fc16e4aec221cacde784b
  languageName: node
  linkType: hard

"@babel/core@npm:7.12.9":
  version: 7.12.9
  resolution: "@babel/core@npm:7.12.9"
  dependencies:
    "@babel/code-frame": ^7.10.4
    "@babel/generator": ^7.12.5
    "@babel/helper-module-transforms": ^7.12.1
    "@babel/helpers": ^7.12.5
    "@babel/parser": ^7.12.7
    "@babel/template": ^7.12.7
    "@babel/traverse": ^7.12.9
    "@babel/types": ^7.12.7
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.1
    json5: ^2.1.2
    lodash: ^4.17.19
    resolve: ^1.3.2
    semver: ^5.4.1
    source-map: ^0.5.0
  checksum: 4d34eca4688214a4eb6bd5dde906b69a7824f17b931f52cd03628a8ac94d8fbe15565aebffdde106e974c8738cd64ac62c6a6060baa7139a06db1f18c4ff872d
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.1, @babel/core@npm:^7.15.5, @babel/core@npm:^7.16.0":
  version: 7.16.7
  resolution: "@babel/core@npm:7.16.7"
  dependencies:
    "@babel/code-frame": ^7.16.7
    "@babel/generator": ^7.16.7
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helpers": ^7.16.7
    "@babel/parser": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.1.2
    semver: ^6.3.0
    source-map: ^0.5.0
  checksum: 3206e077e76db189726c4da19a5296eae11c6c1f5abea7013e74f18708bb91616914717ff8d8ca466cc0ba9d2d2147e9a84c3c357b9ad4cba601da14107838ed
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.12.5, @babel/generator@npm:^7.16.0, @babel/generator@npm:^7.16.7, @babel/generator@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/generator@npm:7.16.8"
  dependencies:
    "@babel/types": ^7.16.8
    jsesc: ^2.5.1
    source-map: ^0.5.0
  checksum: 83af38b34735605c9d5f774c87a46c2cffaf666b28e9eeba883b2d7076412257e5c2264c26d9740ce44da6955fdaf857659391db02c012714a2a6dc19e403105
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.16.0, @babel/helper-annotate-as-pure@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-annotate-as-pure@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: d235be963fed5d48a8a4cfabc41c3f03fad6a947810dbcab9cebed7f819811457e10d99b4b2e942ad71baa7ee8e3cd3f5f38a4e4685639ddfddb7528d9a07179
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.16.7"
  dependencies:
    "@babel/helper-explode-assignable-expression": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 1784f19a57ecfafca8e5c2e0f3eac53451cb13a857cbe0ca0cd9670922228d099ef8c3dd8cd318e2d7bce316fdb2ece3e527c30f3ecd83706e37ab6beb0c60eb
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.13.0, @babel/helper-compilation-targets@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-compilation-targets@npm:7.16.7"
  dependencies:
    "@babel/compat-data": ^7.16.4
    "@babel/helper-validator-option": ^7.16.7
    browserslist: ^4.17.5
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 7238aaee78c011a42fb5ca92e5eff098752f7b314c2111d7bb9cdd58792fcab1b9c819b59f6a0851dc210dc09dc06b30d130a23982753e70eb3111bc65204842
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-create-class-features-plugin@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-member-expression-to-functions": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 55f4eccb93ce12c3a5d9acadf8176ed26a6f02db12d3b2d1a7337bb81139677c7b6fb54ddc01c160895e1f8134946b3bebe59428ef3a7cd9b62692bd3a2c654f
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    regexpu-core: ^4.7.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f6015e0b81deddcbf09fde6c39d3acd55aa3ad45cbf04dae5e2ce2432cd5a63c4a0fa67eaeaa13c6cc526e7618234b9d252c924a5c99a01e6ce8ae882d485f38
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.3.0":
  version: 0.3.0
  resolution: "@babel/helper-define-polyfill-provider@npm:0.3.0"
  dependencies:
    "@babel/helper-compilation-targets": ^7.13.0
    "@babel/helper-module-imports": ^7.12.13
    "@babel/helper-plugin-utils": ^7.13.0
    "@babel/traverse": ^7.13.0
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
    semver: ^6.1.2
  peerDependencies:
    "@babel/core": ^7.4.0-0
  checksum: 372378ac4235c4fe135f1cd6d0f63697e7cb3ef63a884eb14f4b439984846bcaec0b7a32cf8df6756a21557ae3ebb3c2ee18d9a191260705a583333e5e60df7c
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-environment-visitor@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: c03a10105d9ebd1fe632a77356b2e6e2f3c44edba9a93b0dc3591b6a66bd7a2e323dd9502f9ce96fc6401234abff1907aa877b6674f7826b61c953f7c8204bbe
  languageName: node
  linkType: hard

"@babel/helper-explode-assignable-expression@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-explode-assignable-expression@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: ea2135ba36da6a2be059ebc8f10fbbb291eb0e312da54c55c6f50f9cbd8601e2406ec497c5e985f7c07a97f31b3bef9b2be8df53f1d53b974043eaf74fe54bbc
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-function-name@npm:7.16.7"
  dependencies:
    "@babel/helper-get-function-arity": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: fc77cbe7b10cfa2a262d7a37dca575c037f20419dfe0c5d9317f589599ca24beb5f5c1057748011159149eaec47fe32338c6c6412376fcded68200df470161e1
  languageName: node
  linkType: hard

"@babel/helper-get-function-arity@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-get-function-arity@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 25d969fb207ff2ad5f57a90d118f6c42d56a0171022e200aaa919ba7dc95ae7f92ec71cdea6c63ef3629a0dc962ab4c78e09ca2b437185ab44539193f796e0c3
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-hoist-variables@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 6ae1641f4a751cd9045346e3f61c3d9ec1312fd779ab6d6fecfe2a96e59a481ad5d7e40d2a840894c13b3fd6114345b157f9e3062fc5f1580f284636e722de60
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-member-expression-to-functions@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: e275378022278a7e7974a3f65566690f1804ac88c5f4e848725cf936f61cd1e2557e88cfb6cb4fea92ae5a95ad89d78dbccc9a53715d4363f84c9fd109272c18
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.10.4, @babel/helper-module-imports@npm:^7.12.13, @babel/helper-module-imports@npm:^7.16.0, @babel/helper-module-imports@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-module-imports@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: ddd2c4a600a2e9a4fee192ab92bf35a627c5461dbab4af31b903d9ba4d6b6e59e0ff3499fde4e2e9a0eebe24906f00b636f8b4d9bd72ff24d50e6618215c3212
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.12.1, @babel/helper-module-transforms@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-module-transforms@npm:7.16.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-simple-access": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    "@babel/helper-validator-identifier": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 6e930ce776c979f299cdbeaf80187f4ab086d75287b96ecc1c6896d392fcb561065f0d6219fc06fa79b4ceb4bbdc1a9847da8099aba9b077d0a9e583500fb673
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-optimise-call-expression@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 925feb877d5a30a71db56e2be498b3abbd513831311c0188850896c4c1ada865eea795dce5251a1539b0f883ef82493f057f84286dd01abccc4736acfafe15ea
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:7.10.4":
  version: 7.10.4
  resolution: "@babel/helper-plugin-utils@npm:7.10.4"
  checksum: 639ed8fc462b97a83226cee6bb081b1d77e7f73e8b033d2592ed107ee41d96601e321e5ea53a33e47469c7f1146b250a3dcda5ab873c7de162ab62120c341a41
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.13.0, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.16.7, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.16.7
  resolution: "@babel/helper-plugin-utils@npm:7.16.7"
  checksum: d08dd86554a186c2538547cd537552e4029f704994a9201d41d82015c10ed7f58f9036e8d1527c3760f042409163269d308b0b3706589039c5f1884619c6d4ce
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/helper-remap-async-to-generator@npm:7.16.8"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-wrap-function": ^7.16.8
    "@babel/types": ^7.16.8
  checksum: 29282ee36872130085ca111539725abbf20210c2a1d674bee77f338a57c093c3154108d03a275f602e471f583bd2c7ae10d05534f87cbc22b95524fe2b569488
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-replace-supers@npm:7.16.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-member-expression-to-functions": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: e5c0b6eb3dad8410a6255f93b580dde9b3c1564646c6ef751de59d5b2a65b5caa80cc9e568155f04bbae895ad0f54305c2e833dbd971a4f641f970c90b3d892b
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-simple-access@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 8d22c46c5ec2ead0686c4d5a3d1d12b5190c59be676bfe0d9d89df62b437b51d1a3df2ccfb8a77dded2e585176ebf12986accb6d45a18cff229eef3b10344f4b
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.16.0":
  version: 7.16.0
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.16.0"
  dependencies:
    "@babel/types": ^7.16.0
  checksum: b9ed2896eb253e6a85f472b0d4098ed80403758ad1a4e34b02b11e8276e3083297526758b1a3e6886e292987266f10622d7dbced3508cc22b296a74903b41cfb
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-split-export-declaration@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: e10aaf135465c55114627951b79115f24bc7af72ecbb58d541d66daf1edaee5dde7cae3ec8c3639afaf74526c03ae3ce723444e3b5b3dc77140c456cd84bcaa1
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-validator-identifier@npm:7.16.7"
  checksum: dbb3db9d184343152520a209b5684f5e0ed416109cde82b428ca9c759c29b10c7450657785a8b5c5256aa74acc6da491c1f0cf6b784939f7931ef82982051b69
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-validator-option@npm:7.16.7"
  checksum: c5ccc451911883cc9f12125d47be69434f28094475c1b9d2ada7c3452e6ac98a1ee8ddd364ca9e3f9855fcdee96cdeafa32543ebd9d17fee7a1062c202e80570
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/helper-wrap-function@npm:7.16.8"
  dependencies:
    "@babel/helper-function-name": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.8
    "@babel/types": ^7.16.8
  checksum: d8aae4bacaf138d47dca1421ba82b41eac954cbb0ad17ab1c782825c6f2afe20076fbed926ab265967758336de5112d193a363128cd1c6967c66e0151174f797
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.12.5, @babel/helpers@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helpers@npm:7.16.7"
  dependencies:
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 75504c76b66a29b91f954fcc0867dfe275a4cfba5b44df6d64405df74ea72f967fccfa63d62c31c423c5502d113290000c581e0e4858a214f0303d7ecf55c29f
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/highlight@npm:7.16.7"
  dependencies:
    "@babel/helper-validator-identifier": ^7.16.7
    chalk: ^2.0.0
    js-tokens: ^4.0.0
  checksum: f7e04e7e03b83c2cca984f4d3e180c9b018784f45d03367e94daf983861229ddc47264045f3b58dfeb0007f9c67bc2a76c4de1693bad90e5394876ef55ece5bb
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.12.7, @babel/parser@npm:^7.16.4, @babel/parser@npm:^7.16.7, @babel/parser@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/parser@npm:7.16.8"
  bin:
    parser: ./bin/babel-parser.js
  checksum: f6bc2eb1f298fcb81db34c2d343fd05d8c59dbc5419a88c1cb4d298c7a3863e4d54f5a4f38a40e1aa979e4ce355816348730b471c1d787d424ed52b270fc7be0
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: bbb0f82a4cf297bdbb9110eea570addd4b883fd1b61535558d849822b087aa340fe4e9c31f8a39b087595c8310b58d0f5548d6be0b72c410abefb23a5734b7bc
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.16.0
    "@babel/plugin-proposal-optional-chaining": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 81b372651a7d886a06596b02df7fb65ea90265a8bd60c9f0d5c1777590a598e6cccbdc3239033ee0719abf904813e69577eeb0ed5960b40e07978df023b17a6a
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.16.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-remap-async-to-generator": ^7.16.8
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: abd2c2c67de262720d37c5509dafe2ce64d6cee2dc9a8e863bbba1796b77387214442f37618373c6a4521ca624bfc7dcdbeb1376300d16f2a474405ee0ca2e69
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-class-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3977e841e17b45b47be749b9a5b67b9e8b25ff0840f9fdad3f00cbcb35db4f5ff15f074939fe19b01207a29688c432cc2c682351959350834d62920b7881f803
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-static-block@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-class-static-block@npm:7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 3b95b5137e089f0be17de667299ea2e28867b6310ab94219a5a89ac7675824e69f316d31930586142b9f432122ef3b98eb05fffdffae01b5587019ce9aab4ef3
  languageName: node
  linkType: hard

"@babel/plugin-proposal-dynamic-import@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-dynamic-import@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5992012484fb8bda1451369350e475091954ed414dd9ef8654a3c4daa2db0205d4f29c94f5d3dedfbc5a434996375c8304586904337d6af938ac0f27a0033e23
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-namespace-from@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-export-namespace-from@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5016079a5305c1c130fea587b42cdce501574739cfefa5b63469dbc1f32d436df0ff42fabf04089fe8b6a00f4ea7563869e944744b457e186c677995983cb166
  languageName: node
  linkType: hard

"@babel/plugin-proposal-json-strings@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-json-strings@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ea6487918f8d88322ac2a4e5273be6163b0d84a34330c31cee346e23525299de3b4f753bc987951300a79f55b8f4b1971b24d04c0cdfcb7ceb4d636975c215e8
  languageName: node
  linkType: hard

"@babel/plugin-proposal-logical-assignment-operators@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-logical-assignment-operators@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c4cf18e10f900d40eaa471c4adce4805e67bd845f997a4b9d5653eced4e653187b9950843b2bf7eab6c0c3e753aba222b1d38888e3e14e013f87295c5b014f19
  languageName: node
  linkType: hard

"@babel/plugin-proposal-nullish-coalescing-operator@npm:^7.16.0, @babel/plugin-proposal-nullish-coalescing-operator@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-nullish-coalescing-operator@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bfafc2701697b5c763dbbb65dd97b56979bfb0922e35be27733699a837aeff22316313ddfdd0fb45129efa3f86617219b77110d05338bc4dca4385d8ce83dd19
  languageName: node
  linkType: hard

"@babel/plugin-proposal-numeric-separator@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-numeric-separator@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8e2fb0b32845908c67f80bc637a0968e28a66727d7ffb22b9c801dc355d88e865dc24aec586b00c922c23833ae5d26301b443b53609ea73d8344733cd48a1eca
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:7.12.1":
  version: 7.12.1
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.12.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.0
    "@babel/plugin-transform-parameters": ^7.12.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 221a41630c9a7162bf0416c71695b3f7f38482078a1d0d3af7abdc4f07ea1c9feed890399158d56c1d0278c971fe6f565ce822e9351e4481f7d98e9ff735dced
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.16.7"
  dependencies:
    "@babel/compat-data": ^7.16.4
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2d3740e4df6d3f51d57862100c45c000104571aa98b7f798fdfc05ae0c12b9e7cc9b55f4a28612d626e29f3369a1481a0ee8a0241b23508b9d3da00c55f99d41
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4a422bb19a23cf80a245c60bea7adbe5dac8ff3bc1a62f05d7155e1eb68d401b13339c94dfd1f3d272972feeb45746f30d52ca0f8d5c63edf6891340878403df
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-chaining@npm:^7.16.0, @babel/plugin-proposal-optional-chaining@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-optional-chaining@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.16.0
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e4a6c1ac7e6817b92a673ea52ab0b7dc1fb39d29fb0820cd414e10ae2cd132bd186b4238dcca881a29fc38fe9d38ed24fc111ba22ca20086481682d343f4f130
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-methods@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-private-methods@npm:7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1d3aef6f1818278d257ed6f1a90ec3b0cfe85e1b24cabf1bcd0d2a0033f8ae15f9cb36140ec2adc2a317f63fc78095ce0b5c154f73128e0f84480879a4b64269
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 666d668f51d8c01aaf0dd87b27a83fc0392884d2c8e9d8e17b3b7011c0d348865dee94b44dc2d7070726e58e3b579728dc2588aaa8140d563f7390743ee90f0a
  languageName: node
  linkType: hard

"@babel/plugin-proposal-unicode-property-regex@npm:^7.16.7, @babel/plugin-proposal-unicode-property-regex@npm:^7.4.4":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-unicode-property-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b8a33713d456183f0b7d011011e7bd932c08cc06216399a7b2015ab39284b511993dc10a89bbb15d1d728e6a2ef42ca08c3202619aa148cbd48052422ea3995
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:7.12.1":
  version: 7.12.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.12.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d4b9b589c484b2e0856799770f060dff34c67b24d7f4526f66309a0e0e9cf388a5c1f2c0da329d1973cc87d1b2cede8f3dc8facfac59e785d6393a003bcdd0f9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-syntax-jsx@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cd9b0e53c50e8ddb0afaf0f42e0b221a94e4f59aee32a591364266a31195c48cac5fef288d02c1c935686bda982d2e0f1ed61cceb995fc9f6fb09ef5ebecdd2b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:7.8.3, @babel/plugin-syntax-object-rest-spread@npm:^7.8.0, @babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-syntax-typescript@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 661e636060609ede9a402e22603b01784c21fabb0a637e65f561c8159351fe0130bbc11fdefe31902107885e3332fc34d95eb652ac61d3f61f2d61f5da20609e
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2a6aa982c6fc80f4de7ccd973507ce5464fab129987cb6661136a7b9b6a020c2b329b912cbc46a68d39b5a18451ba833dcc8d1ca8d615597fec98624ac2add54
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.16.8"
  dependencies:
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-remap-async-to-generator": ^7.16.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a2e781800e3dea1f526324ed259d1f9064c5ea3c9909c0c22b445d4c648ad489c579f358ae20ada11f7725ba67e0ddeb1e0241efadc734771e87dabd4c6820a
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 591e9f75437bb32ebf9506d28d5c9659c66c0e8e0c19b12924d808d898e68309050aadb783ccd70bb4956555067326ecfa17a402bc77eb3ece3c6863d40b9016
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-block-scoping@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f93b5441af573fc274655f1707aeb4f67a43e926b58f56d89cc35a27877ae0bf198648603cbc19f442579489138f93c3838905895f109aa356996dbc3ed97a68
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-classes@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 791526a1bf3c4659b94d619536e3181d3ad54887d50539066628c6e695789a3bb264dc1fbc8540169d62a222f623df54defb490c1811ae63bad1e3557d6b3bb0
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-computed-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 28b17f7cfe643f45920b76dc040cab40d4e54eccf5074fba2658c484feacda9b4885b3854ffaf26292189783fdecc97211519c61831b6708fcbf739cfbcbf31c
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-destructuring@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d1c2e15e7be2a7c57ac8ec4df06fbb706c7ecc872ab7bc2193606e6d6a01929b6d5a1bb41540e41180e42a5ce0e70dce22e7896cb6578dd581d554f77780971b
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.16.7, @babel/plugin-transform-dotall-regex@npm:^7.4.4":
  version: 7.16.7
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 554570dddfd5bfd87ab307be520f69a3d4ed2d2db677c165971b400d4c96656d0c165b318e69f1735612dcd12e04c0ee257697dc26800e8a572ca73bc05fa0f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b96f6e9f7b33a91ad0eb6b793e4da58b7a0108b58269109f391d57078d26e043b3872c95429b491894ae6400e72e44d9b744c9b112b8433c99e6969b767e30ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.16.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8082c79268f5b1552292bd3abbfed838a1131747e62000146e70670707b518602e907bbe3aef0fda824a2eebe995a9d897bd2336a039c5391743df01608673b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-for-of@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35c9264ee4bef814818123d70afe8b2f0a85753a0a9dc7b73f93a71cadc5d7de852f1a3e300a7c69a491705805704611de1e2ccceb5686f7828d6bca2e5a7306
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-function-name@npm:7.16.7"
  dependencies:
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d97d0b84461cdd5d5aa2d010cdaf30f1f83a92a0dedd3686cbc7e90dc1249a70246f5bac0c1f3cd3f1dbfb03f7aac437776525a0c90cafd459776ea4fcc6bde
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a9565d999fc7a72a391ef843cf66028c38ca858537c7014d9ea8ea587a59e5f952d9754bdcca6ca0446e84653e297d417d4faedccb9e4221af1aa30f25d918e0
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fdf5b22abab2b770e69348ce7f99796c3e0e1e7ce266afdbe995924284704930fa989323bdbda7070db8adb45a72f39eaa1dbebf18b67fc44035ec00c6ae3300
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-amd@npm:7.16.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ac251ee96183b10cf9b4ec8f9e8d52e14ec186a56103f6c07d0c69e99faa60391f6bac67da733412975e487bd36adb403e2fc99bae6b785bf1413e9d928bc71
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.16.8"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-simple-access": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c0ac00f5457e12cac7825b14725b6fc787bef78945181469ff79f07ef0fd7df021cb00fe1d3a9f35fc9bc92ae59e6e3fc9075a70b627dfe10e00d0907892aace
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.16.7"
  dependencies:
    "@babel/helper-hoist-variables": ^7.16.7
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-validator-identifier": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2e50ae45a725eeafac5a9d30e07a5e17ab8dcf62c3528cf4efe444fc6f12cd3c4e42e911a9aa37abab169687a98b29a4418eeafcf2031f9917162ac36105cb1b
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-umd@npm:7.16.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d1433f8b0e0b3c9f892aa530f08fe3ba653a5e51fe1ed6034ac7d45d4d6f22c3ba99186b72e41ad9ce5d8dcf964104c3da2419f15fcdcf5ba05c5fda3ea2cefc
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.16.8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 73e149f5ff690f5b8e3764a881e8e5240f12f394256e7d5217705d0cbeae074c3faff394783190fe1a41f9fc5a53b960b6021158b7e5174391b5fc38f4ba047a
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-new-target@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7410c3e68abc835f87a98d40269e65fb1a05c131decbb6721a80ed49a01bd0c53abb6b8f7f52d5055815509022790e1accca32e975c02f2231ac3cf13d8af768
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-object-super@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46e3c879f4a93e904f2ecf83233d40c48c832bdbd82a67cab1f432db9aa51702e40d9e51e5800613e12299974f90f4ed3869e1273dbca8642984266320c5f341
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.12.1, @babel/plugin-transform-parameters@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-parameters@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d6904376db82d0b35f0a6cce08f630daf8608d94e903d6c7aff5bd742b251651bd1f88cdf9f16cad98aba5fc7c61da8635199364865fad6367d5ae37cf56cc1
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-property-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b5674458991a9b0e8738989d70faa88c7f98ed3df923c119f1225069eed72fe5e0ce947b1adc91e378f5822fbdeb7a672f496fd1c75c4babcc88169e3a7c3229
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-constant-elements@npm:^7.14.5":
  version: 7.16.7
  resolution: "@babel/plugin-transform-react-constant-elements@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b2c586deba5ca86ebb4e26d01a26d056e48fd6f64760676a4b6a0be6f81b8afdd91189e976b57ba18ff966971f92f5ce97fd08798c9bf1a687e6e71bf4198b87
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-react-display-name@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 483154413671ab0a25ae37520b7cf5bfab0958c484a3707c6799b1f1436d1e51481bcc03fbfcdbf90bf6b46818d931ae35e515141d8354c3287351b4467376ba
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.16.7"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 697c71cb0ac9647a9b8c6f1aca99767cf06197f6c0b5d1f2e0c01f641e0706a380779f06836fdb941d3aa171f868091270fbe9fcfbfbcc2a24df5e60e04545e8
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-react-jsx@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-jsx": ^7.16.7
    "@babel/types": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0e82346d7c99b4467946d535a8c626a988e5670f65a15dee8520ce9cf4f0147c99decc1cbb4bd352083eaafd259ee3e4299854cac6304a83666d488edf4e58f6
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 715fe9c5fd10c5605a6de1d4436d29087878924758969427ba4d0b2bc274a436d3ac8f2777b744c988bdbb90f7e68dc2a82491db333ae7e0079fab776b543fae
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-regenerator@npm:7.16.7"
  dependencies:
    regenerator-transform: ^0.14.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 12b1f9a4f324027af69f49522fbe7feea2ac53285ca5c7e27a70de09f56c74938bfda8b09ac06e57fa1207e441f00efb7adbc462afc9be5e8abd0c2a07715e01
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-reserved-words@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 00218a646e99a97c1f10b77c41c178ca1b91d0e6cf18dd4ca3c59b8a5ad721db04ef508f49be4cd0dcca7742490dbb145307b706a2dbea1917d5e5f7ba2f31b7
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.16.0":
  version: 7.16.8
  resolution: "@babel/plugin-transform-runtime@npm:7.16.8"
  dependencies:
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    babel-plugin-polyfill-corejs2: ^0.3.0
    babel-plugin-polyfill-corejs3: ^0.5.0
    babel-plugin-polyfill-regenerator: ^0.3.0
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e5cbbe2a57115f43c958be42fe417180075f024752d5e80deb935838791b212fe3fcd9d6f8f80a141fcdc9f9bd7bac2e373abb2bd89d439f669db9c272061859
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ca381ecf8f48696512172deca40af46b1f64e3497186fdc2c9009286d8f06b468c4d61cdc392dc8b0c165298117dda67be9e2ff0e99d7691b0503f1240d4c62b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-spread@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.16.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6e961af1a70586bb72dd85e8296cee857c5dadd73225fccd0fe261c0d98652a82d69c65f3e9dc31ce019a12e9677262678479b96bd2d9140ddf6514618362828
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d59e20121ff0a483e29364eff8bb42cd8a0b7a3158141eea5b6f219227e5b873ea70f317f65037c0f557887a692ac993b72f99641a37ea6ec0ae8000bfab1343
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-template-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b55a519dd8b957247ebad3cab21918af5adca4f6e6c87819501cfe3d4d4bccda25bc296c7dfc8a30909b4ad905902aeb9d55ad955cb9f5cbc74b42dab32baa18
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 739a8c439dacbd9af62cfbfa0a7cbc3f220849e5fc774e5ef708a09186689a724c41a1d11323e7d36588d24f5481c8b702c86ff7be8da2e2fed69bed0175f625
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.16.7":
  version: 7.16.8
  resolution: "@babel/plugin-transform-typescript@npm:7.16.8"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-typescript": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a76d0afcbd550208cf2e7cdedb4f2d3ca3fa287640a4858a5ee0a28270b784d7d20d5a51b5997dc84514e066a5ebef9e0a0f74ed9fffae09e73984786dd08036
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d10c3b5baa697ca2d9ecce2fd7705014d7e1ddd86ed684ccec378f7ad4d609ab970b5546d6cdbe242089ecfc7a79009d248cf4f8ee87d629485acfb20c0d9160
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ef7721cfb11b269809555b1c392732566c49f6ced58e0e990c0e81e58a934bbab3072dcbe92d3a20d60e3e41036ecf987bcc63a7cde90711a350ad774667e5e6
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.11.0, @babel/preset-env@npm:^7.15.6, @babel/preset-env@npm:^7.16.4":
  version: 7.16.8
  resolution: "@babel/preset-env@npm:7.16.8"
  dependencies:
    "@babel/compat-data": ^7.16.8
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-validator-option": ^7.16.7
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.16.7
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.16.7
    "@babel/plugin-proposal-async-generator-functions": ^7.16.8
    "@babel/plugin-proposal-class-properties": ^7.16.7
    "@babel/plugin-proposal-class-static-block": ^7.16.7
    "@babel/plugin-proposal-dynamic-import": ^7.16.7
    "@babel/plugin-proposal-export-namespace-from": ^7.16.7
    "@babel/plugin-proposal-json-strings": ^7.16.7
    "@babel/plugin-proposal-logical-assignment-operators": ^7.16.7
    "@babel/plugin-proposal-nullish-coalescing-operator": ^7.16.7
    "@babel/plugin-proposal-numeric-separator": ^7.16.7
    "@babel/plugin-proposal-object-rest-spread": ^7.16.7
    "@babel/plugin-proposal-optional-catch-binding": ^7.16.7
    "@babel/plugin-proposal-optional-chaining": ^7.16.7
    "@babel/plugin-proposal-private-methods": ^7.16.7
    "@babel/plugin-proposal-private-property-in-object": ^7.16.7
    "@babel/plugin-proposal-unicode-property-regex": ^7.16.7
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-transform-arrow-functions": ^7.16.7
    "@babel/plugin-transform-async-to-generator": ^7.16.8
    "@babel/plugin-transform-block-scoped-functions": ^7.16.7
    "@babel/plugin-transform-block-scoping": ^7.16.7
    "@babel/plugin-transform-classes": ^7.16.7
    "@babel/plugin-transform-computed-properties": ^7.16.7
    "@babel/plugin-transform-destructuring": ^7.16.7
    "@babel/plugin-transform-dotall-regex": ^7.16.7
    "@babel/plugin-transform-duplicate-keys": ^7.16.7
    "@babel/plugin-transform-exponentiation-operator": ^7.16.7
    "@babel/plugin-transform-for-of": ^7.16.7
    "@babel/plugin-transform-function-name": ^7.16.7
    "@babel/plugin-transform-literals": ^7.16.7
    "@babel/plugin-transform-member-expression-literals": ^7.16.7
    "@babel/plugin-transform-modules-amd": ^7.16.7
    "@babel/plugin-transform-modules-commonjs": ^7.16.8
    "@babel/plugin-transform-modules-systemjs": ^7.16.7
    "@babel/plugin-transform-modules-umd": ^7.16.7
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.16.8
    "@babel/plugin-transform-new-target": ^7.16.7
    "@babel/plugin-transform-object-super": ^7.16.7
    "@babel/plugin-transform-parameters": ^7.16.7
    "@babel/plugin-transform-property-literals": ^7.16.7
    "@babel/plugin-transform-regenerator": ^7.16.7
    "@babel/plugin-transform-reserved-words": ^7.16.7
    "@babel/plugin-transform-shorthand-properties": ^7.16.7
    "@babel/plugin-transform-spread": ^7.16.7
    "@babel/plugin-transform-sticky-regex": ^7.16.7
    "@babel/plugin-transform-template-literals": ^7.16.7
    "@babel/plugin-transform-typeof-symbol": ^7.16.7
    "@babel/plugin-transform-unicode-escapes": ^7.16.7
    "@babel/plugin-transform-unicode-regex": ^7.16.7
    "@babel/preset-modules": ^0.1.5
    "@babel/types": ^7.16.8
    babel-plugin-polyfill-corejs2: ^0.3.0
    babel-plugin-polyfill-corejs3: ^0.5.0
    babel-plugin-polyfill-regenerator: ^0.3.0
    core-js-compat: ^3.20.2
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 834fc0638b26e9a118a4deaac828ebb9cc17c4d1aa6497e6588003de1ff3768b1ee9bfddd055ccc6be526c65b92d5c463168da2425d26a53c836b38eeb3362cb
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:^0.1.5":
  version: 0.1.5
  resolution: "@babel/preset-modules@npm:0.1.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/plugin-proposal-unicode-property-regex": ^7.4.4
    "@babel/plugin-transform-dotall-regex": ^7.4.4
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8430e0e9e9d520b53e22e8c4c6a5a080a12b63af6eabe559c2310b187bd62ae113f3da82ba33e9d1d0f3230930ca702843aae9dd226dec51f7d7114dc1f51c10
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.14.5, @babel/preset-react@npm:^7.16.0":
  version: 7.16.7
  resolution: "@babel/preset-react@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-validator-option": ^7.16.7
    "@babel/plugin-transform-react-display-name": ^7.16.7
    "@babel/plugin-transform-react-jsx": ^7.16.7
    "@babel/plugin-transform-react-jsx-development": ^7.16.7
    "@babel/plugin-transform-react-pure-annotations": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d0a052a418891ab6a02df9c75f0202964ad3b936c20bc44c81bcf3f02c057383f2fa329e0cc79baaac1b4e5e5c8924d3df93a2dd9319efe8042e3b33849978b3
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.15.0, @babel/preset-typescript@npm:^7.16.0":
  version: 7.16.7
  resolution: "@babel/preset-typescript@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-validator-option": ^7.16.7
    "@babel/plugin-transform-typescript": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 44e2f3fa302befe0dc50a01b79e5aa8c27a9c7047c46df665beae97201173030646ddf7c83d7d3ed3724fc38151745b11693e7b4502c81c4cd67781ff5677da5
  languageName: node
  linkType: hard

"@babel/runtime-corejs3@npm:^7.16.3":
  version: 7.16.8
  resolution: "@babel/runtime-corejs3@npm:7.16.8"
  dependencies:
    core-js-pure: ^3.20.2
    regenerator-runtime: ^0.13.4
  checksum: 3d8fe2f3030c01e8725b9e0985b403463fae2081ca46f16bf257f8e7f32e2ebc37065499941de8678b3ba46145b19db6a7d4c8ac3b675331c7284dd3cdd1dc62
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.1.2, @babel/runtime@npm:^7.10.2, @babel/runtime@npm:^7.10.3, @babel/runtime@npm:^7.11.2, @babel/runtime@npm:^7.12.1, @babel/runtime@npm:^7.12.13, @babel/runtime@npm:^7.16.3, @babel/runtime@npm:^7.8.4":
  version: 7.16.7
  resolution: "@babel/runtime@npm:7.16.7"
  dependencies:
    regenerator-runtime: ^0.13.4
  checksum: 47912f0aaacd1cab2e2552aaf3e6eaffbcaf2d5ac9b07a89a12ac0d42029cb92c070b0d16f825e4277c4a34677c54d8ffe85e1f7c6feb57de58f700eec67ce2f
  languageName: node
  linkType: hard

"@babel/template@npm:^7.12.7, @babel/template@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/template@npm:7.16.7"
  dependencies:
    "@babel/code-frame": ^7.16.7
    "@babel/parser": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 10cd112e89276e00f8b11b55a51c8b2f1262c318283a980f4d6cdb0286dc05734b9aaeeb9f3ad3311900b09bc913e02343fcaa9d4a4f413964aaab04eb84ac4a
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.12.9, @babel/traverse@npm:^7.13.0, @babel/traverse@npm:^7.16.3, @babel/traverse@npm:^7.16.7, @babel/traverse@npm:^7.16.8, @babel/traverse@npm:^7.4.5":
  version: 7.16.8
  resolution: "@babel/traverse@npm:7.16.8"
  dependencies:
    "@babel/code-frame": ^7.16.7
    "@babel/generator": ^7.16.8
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-hoist-variables": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    "@babel/parser": ^7.16.8
    "@babel/types": ^7.16.8
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: 303bc328289c73bd57dc8b90e83dfa9f4dae8e7039c95350994db67b2850a7966645c2c9f3292d0621f2051bb3d34439dc294b258dc1ad0e9d7eab04ac6bcb44
  languageName: node
  linkType: hard

"@babel/types@npm:^7.12.7, @babel/types@npm:^7.15.6, @babel/types@npm:^7.16.0, @babel/types@npm:^7.16.7, @babel/types@npm:^7.16.8, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.16.8
  resolution: "@babel/types@npm:7.16.8"
  dependencies:
    "@babel/helper-validator-identifier": ^7.16.7
    to-fast-properties: ^2.0.0
  checksum: 4f6a187b2924df70e21d6e6c0822f91b1b936fe060bc92bb477b93bd8a712c88fe41a73f85c0ec53b033353374fe33e773b04ffc340ad36afd8f647dd05c4ee1
  languageName: node
  linkType: hard

"@docsearch/css@npm:3.0.0-alpha.42":
  version: 3.0.0-alpha.42
  resolution: "@docsearch/css@npm:3.0.0-alpha.42"
  checksum: 83ddb4e3fd1ff4969a2f474857df58eca88c0246b6e0d3ca4a6e028e4ad26712258534ed0030a5071e232f89e59b535c36c6af2de11a30e539df4a53825e41fc
  languageName: node
  linkType: hard

"@docsearch/react@npm:^3.0.0-alpha.39":
  version: 3.0.0-alpha.42
  resolution: "@docsearch/react@npm:3.0.0-alpha.42"
  dependencies:
    "@algolia/autocomplete-core": 1.5.0
    "@algolia/autocomplete-preset-algolia": 1.5.0
    "@docsearch/css": 3.0.0-alpha.42
    algoliasearch: ^4.0.0
  peerDependencies:
    "@types/react": ">= 16.8.0 < 18.0.0"
    react: ">= 16.8.0 < 18.0.0"
    react-dom: ">= 16.8.0 < 18.0.0"
  checksum: 4766b44059d598a961ed58b6aa5a958fcd2c8749f98b601c971d77f2da3d3fec5c4e07a98e5d0b93e6f1a0417248ef9ecef0378198c15337090cb70c88556d35
  languageName: node
  linkType: hard

"@docusaurus/core@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/core@npm:2.0.0-beta.14"
  dependencies:
    "@babel/core": ^7.16.0
    "@babel/generator": ^7.16.0
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-transform-runtime": ^7.16.0
    "@babel/preset-env": ^7.16.4
    "@babel/preset-react": ^7.16.0
    "@babel/preset-typescript": ^7.16.0
    "@babel/runtime": ^7.16.3
    "@babel/runtime-corejs3": ^7.16.3
    "@babel/traverse": ^7.16.3
    "@docusaurus/cssnano-preset": 2.0.0-beta.14
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/mdx-loader": 2.0.0-beta.14
    "@docusaurus/react-loadable": 5.5.2
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-common": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    "@slorber/static-site-generator-webpack-plugin": ^4.0.0
    "@svgr/webpack": ^6.0.0
    autoprefixer: ^10.3.5
    babel-loader: ^8.2.2
    babel-plugin-dynamic-import-node: 2.3.0
    boxen: ^5.0.1
    chokidar: ^3.5.2
    clean-css: ^5.1.5
    commander: ^5.1.0
    copy-webpack-plugin: ^9.0.1
    core-js: ^3.18.0
    css-loader: ^5.1.1
    css-minimizer-webpack-plugin: ^3.0.2
    cssnano: ^5.0.8
    del: ^6.0.0
    detect-port: ^1.3.0
    escape-html: ^1.0.3
    eta: ^1.12.3
    file-loader: ^6.2.0
    fs-extra: ^10.0.0
    globby: ^11.0.2
    html-minifier-terser: ^6.0.2
    html-tags: ^3.1.0
    html-webpack-plugin: ^5.4.0
    import-fresh: ^3.3.0
    is-root: ^2.1.0
    leven: ^3.1.0
    lodash: ^4.17.20
    mini-css-extract-plugin: ^1.6.0
    nprogress: ^0.2.0
    postcss: ^8.3.7
    postcss-loader: ^6.1.1
    prompts: ^2.4.1
    react-dev-utils: 12.0.0-next.47
    react-error-overlay: ^6.0.9
    react-helmet: ^6.1.0
    react-loadable: "npm:@docusaurus/react-loadable@5.5.2"
    react-loadable-ssr-addon-v5-slorber: ^1.0.1
    react-router: ^5.2.0
    react-router-config: ^5.1.1
    react-router-dom: ^5.2.0
    remark-admonitions: ^1.2.1
    resolve-pathname: ^3.0.0
    rtl-detect: ^1.0.4
    semver: ^7.3.4
    serve-handler: ^6.1.3
    shelljs: ^0.8.4
    strip-ansi: ^6.0.0
    terser-webpack-plugin: ^5.2.4
    tslib: ^2.3.1
    update-notifier: ^5.1.0
    url-loader: ^4.1.1
    wait-on: ^6.0.0
    webpack: ^5.61.0
    webpack-bundle-analyzer: ^4.4.2
    webpack-dev-server: ^4.5.0
    webpack-merge: ^5.8.0
    webpackbar: ^5.0.0-3
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  bin:
    docusaurus: bin/docusaurus.js
  checksum: 6a5a89ecbb55c8436b3463748e4869d7adb0bbf781dcdb0559b8ac96089685e51f494557462841bbbf5d1eef04344004cf637fde119bf600063489361c1be573
  languageName: node
  linkType: hard

"@docusaurus/cssnano-preset@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/cssnano-preset@npm:2.0.0-beta.14"
  dependencies:
    cssnano-preset-advanced: ^5.1.4
    postcss: ^8.3.7
    postcss-sort-media-queries: ^4.1.0
  checksum: 415e3b15ad51ed669bab463c4f27d1ce8afeb2513d5b7126068a1827b2154c55e6b37a7ffaad7c9ccad2011f9611cc7d70782ee56e06a6d725b38c581b69a0ac
  languageName: node
  linkType: hard

"@docusaurus/logger@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/logger@npm:2.0.0-beta.14"
  dependencies:
    chalk: ^4.1.2
    tslib: ^2.3.1
  checksum: 4670f3f4c19d9427f3d554e4b67aee42df19d7c61be9335c6caf6e979eb9c5e6c14cb753f7456318ebdf799ccc9fee9e2283991b2166f5eaa7345ef0ef40a48a
  languageName: node
  linkType: hard

"@docusaurus/mdx-loader@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/mdx-loader@npm:2.0.0-beta.14"
  dependencies:
    "@babel/parser": ^7.16.4
    "@babel/traverse": ^7.16.3
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@mdx-js/mdx": ^1.6.21
    "@mdx-js/react": ^1.6.21
    escape-html: ^1.0.3
    file-loader: ^6.2.0
    fs-extra: ^10.0.0
    gray-matter: ^4.0.3
    mdast-util-to-string: ^2.0.0
    remark-emoji: ^2.1.0
    stringify-object: ^3.3.0
    tslib: ^2.3.1
    unist-util-visit: ^2.0.2
    url-loader: ^4.1.1
    webpack: ^5.61.0
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: 248a8f73b4e016757ad5317e1312a8d2a4c6ee23ccf02782eb8395f5a7e165ae6a64cb9633441e4260e53c7518aa70e61d72c2f6d35246000d92f17a7ddfcfd6
  languageName: node
  linkType: hard

"@docusaurus/module-type-aliases@npm:^2.0.0-beta.14":
  version: 2.0.0-beta.fc64c12e4
  resolution: "@docusaurus/module-type-aliases@npm:2.0.0-beta.fc64c12e4"
  checksum: 95954104112beaa90e6f5a39834c4a5fa1619ba2a0b997d0343e36c58dd1d44f9672f006b6b9b592b5c93137619bcbfc4237b029178b789498d7e41376403d93
  languageName: node
  linkType: hard

"@docusaurus/plugin-content-blog@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-content-blog@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/mdx-loader": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    escape-string-regexp: ^4.0.0
    feed: ^4.2.2
    fs-extra: ^10.0.0
    globby: ^11.0.2
    js-yaml: ^4.0.0
    loader-utils: ^2.0.0
    lodash: ^4.17.20
    reading-time: ^1.5.0
    remark-admonitions: ^1.2.1
    tslib: ^2.3.1
    utility-types: ^3.10.0
    webpack: ^5.61.0
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: 9d98707faa0e577a2f0fe1da562d7fed5a7251fc79bedaf538e334b5b3536375c35238f8e2141d24ee7f15858f169da14d4a83837608dafca2a3168c069b7c05
  languageName: node
  linkType: hard

"@docusaurus/plugin-content-docs@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-content-docs@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/mdx-loader": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    combine-promises: ^1.1.0
    escape-string-regexp: ^4.0.0
    fs-extra: ^10.0.0
    globby: ^11.0.2
    import-fresh: ^3.2.2
    js-yaml: ^4.0.0
    loader-utils: ^2.0.0
    lodash: ^4.17.20
    remark-admonitions: ^1.2.1
    shelljs: ^0.8.4
    tslib: ^2.3.1
    utility-types: ^3.10.0
    webpack: ^5.61.0
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: d7c1951f189906481066a68461f4e4752d8ec7b3ba15de9b2a9ee6f36000a1fe0bcccee205594e8e8fff9bfb6e41a06639b50cf75a4c90fc6c4ac9ecfbe0d807
  languageName: node
  linkType: hard

"@docusaurus/plugin-content-pages@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-content-pages@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/mdx-loader": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    globby: ^11.0.2
    remark-admonitions: ^1.2.1
    tslib: ^2.3.1
    webpack: ^5.61.0
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: 1372f554d8629fc31629e0b77dbe957703204760c1020ef0a32e0d7cfef277eb26244e9db23cad73adecb468db7397b53c169fd53126295f0b3dcbc7453940ca
  languageName: node
  linkType: hard

"@docusaurus/plugin-debug@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-debug@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    fs-extra: ^10.0.0
    react-json-view: ^1.21.3
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: c5316e2d55970a3ebfef5e6a04726eef35ffbc54976bf13791ac2888566b0454b9f60620c4f840e5ef3e216359412754fef30ccef4755726b2e6a3875deae1a8
  languageName: node
  linkType: hard

"@docusaurus/plugin-google-analytics@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-google-analytics@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: 6d36fc808a54ee04afd5713d6e2ed72a1f51256c928d93b04942a960188f4f7f5710300fbaff99f9ea344c26c3305823baaebd0a01cf5a25ed8261ec2f7296bd
  languageName: node
  linkType: hard

"@docusaurus/plugin-google-gtag@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-google-gtag@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: f04ef80c58f64e349daca93a06c5cb91d026ddea7047fe5f788efc2a2ca2b0bbf1b5974fe799bb0d0b1da5cede6a5539ada165bb9a23511ca07d7b0e7ff655d3
  languageName: node
  linkType: hard

"@docusaurus/plugin-pwa@npm:^2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-pwa@npm:2.0.0-beta.14"
  dependencies:
    "@babel/plugin-proposal-nullish-coalescing-operator": ^7.16.0
    "@babel/plugin-proposal-optional-chaining": ^7.16.0
    "@babel/preset-env": ^7.16.4
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/theme-common": 2.0.0-beta.14
    "@docusaurus/theme-translations": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    babel-loader: ^8.2.2
    clsx: ^1.1.1
    core-js: ^3.18.0
    terser-webpack-plugin: ^5.2.4
    tslib: ^2.3.1
    webpack: ^5.61.0
    webpack-merge: ^5.7.3
    workbox-build: ^6.1.1
    workbox-precaching: ^6.1.1
    workbox-window: ^6.1.1
  peerDependencies:
    "@babel/core": ^7.0.0
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: cd6c69219585c9bed31321201688ae44f8f40ebc839a3b5d010d630adacb7a465238e36420165edf9c4a5548c6dc3664c98748b15d6bfce120ec7fea0c943b0d
  languageName: node
  linkType: hard

"@docusaurus/plugin-sitemap@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/plugin-sitemap@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-common": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    fs-extra: ^10.0.0
    sitemap: ^7.0.0
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: eb0408666be6928033ff79565de29da2cda998b8c0b207f1f6595a712e3e9c02df9a159f25a5be9f84a9e55d1df3984ffefb8c3f8808ae7b0e779af91838e23a
  languageName: node
  linkType: hard

"@docusaurus/preset-classic@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/preset-classic@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/plugin-content-blog": 2.0.0-beta.14
    "@docusaurus/plugin-content-docs": 2.0.0-beta.14
    "@docusaurus/plugin-content-pages": 2.0.0-beta.14
    "@docusaurus/plugin-debug": 2.0.0-beta.14
    "@docusaurus/plugin-google-analytics": 2.0.0-beta.14
    "@docusaurus/plugin-google-gtag": 2.0.0-beta.14
    "@docusaurus/plugin-sitemap": 2.0.0-beta.14
    "@docusaurus/theme-classic": 2.0.0-beta.14
    "@docusaurus/theme-search-algolia": 2.0.0-beta.14
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: 67019820c851503a65a8083639fb299e859fe5b9ff49dde82d7b83b7a4f7aad72bcd0c5d0187a1dfcf56fa78db41be788cb1dfcad17117c8d0c562fbb46e4c48
  languageName: node
  linkType: hard

"@docusaurus/react-loadable@npm:5.5.2, react-loadable@npm:@docusaurus/react-loadable@5.5.2":
  version: 5.5.2
  resolution: "@docusaurus/react-loadable@npm:5.5.2"
  dependencies:
    "@types/react": "*"
    prop-types: ^15.6.2
  peerDependencies:
    react: "*"
  checksum: 930fb9e2936412a12461f210acdc154a433283921ca43ac3fc3b84cb6c12eb738b3a3719373022bf68004efeb1a928dbe36c467d7a1f86454ed6241576d936e7
  languageName: node
  linkType: hard

"@docusaurus/theme-classic@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/theme-classic@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/plugin-content-blog": 2.0.0-beta.14
    "@docusaurus/plugin-content-docs": 2.0.0-beta.14
    "@docusaurus/plugin-content-pages": 2.0.0-beta.14
    "@docusaurus/theme-common": 2.0.0-beta.14
    "@docusaurus/theme-translations": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    "@mdx-js/mdx": ^1.6.21
    "@mdx-js/react": ^1.6.21
    clsx: ^1.1.1
    copy-text-to-clipboard: ^3.0.1
    globby: ^11.0.2
    infima: 0.2.0-alpha.37
    lodash: ^4.17.20
    postcss: ^8.3.7
    prism-react-renderer: ^1.2.1
    prismjs: ^1.23.0
    react-router-dom: ^5.2.0
    rtlcss: ^3.3.0
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: ebb4c362e4a2bedcb1539b4ba90751c3942145f482ddf03734df6aafddc558e7ef8075930829d15c19be54637728a63bdefbe2c5db78a2beb3212398ecb2f313
  languageName: node
  linkType: hard

"@docusaurus/theme-common@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/theme-common@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/plugin-content-blog": 2.0.0-beta.14
    "@docusaurus/plugin-content-docs": 2.0.0-beta.14
    "@docusaurus/plugin-content-pages": 2.0.0-beta.14
    clsx: ^1.1.1
    fs-extra: ^10.0.0
    parse-numeric-range: ^1.3.0
    tslib: ^2.3.1
    utility-types: ^3.10.0
  peerDependencies:
    prism-react-renderer: ^1.2.1
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: dbe969d676e901fe730cac2b95e87452e72c5fe31409e5d46c0d49f38da80177f79c20121eaa64b1d19cd60d6a8f179f30b13e0b1724d1e2618164a5f0dc0a2f
  languageName: node
  linkType: hard

"@docusaurus/theme-live-codeblock@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/theme-live-codeblock@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/theme-translations": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    "@philpl/buble": ^0.19.7
    clsx: ^1.1.1
    fs-extra: ^10.0.0
    parse-numeric-range: ^1.3.0
    prism-react-renderer: ^1.2.1
    react-live: 2.2.3
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: b04b1aa29a63985bfa53726f472a5e83b094d157f5738edef866dc7a4de9593999ef427bfe21a88af69d80285776e6f3c0f879db0e070403b15b6dd90d19c4d1
  languageName: node
  linkType: hard

"@docusaurus/theme-search-algolia@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/theme-search-algolia@npm:2.0.0-beta.14"
  dependencies:
    "@docsearch/react": ^3.0.0-alpha.39
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/theme-common": 2.0.0-beta.14
    "@docusaurus/theme-translations": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    "@docusaurus/utils-validation": 2.0.0-beta.14
    algoliasearch: ^4.10.5
    algoliasearch-helper: ^3.5.5
    clsx: ^1.1.1
    eta: ^1.12.3
    lodash: ^4.17.20
    tslib: ^2.3.1
  peerDependencies:
    react: ^16.8.4 || ^17.0.0
    react-dom: ^16.8.4 || ^17.0.0
  checksum: d4dbaebb21e667e254f06e8146259df29c867a5d8927d09358fa233e4d526252afc9210cc446f12c13930a39880aa201ebe34ba423f7a4744475c173197d2dae
  languageName: node
  linkType: hard

"@docusaurus/theme-translations@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/theme-translations@npm:2.0.0-beta.14"
  dependencies:
    fs-extra: ^10.0.0
    tslib: ^2.3.1
  checksum: cb8d81ecc97aad96e3f320cb81bafe13bc9b5e0f509fdc7fbd1758c819aba8b3b50946505d2df2b0c802f3f15472295e49227c5f33037dcd3187ece7f03d227d
  languageName: node
  linkType: hard

"@docusaurus/utils-common@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/utils-common@npm:2.0.0-beta.14"
  dependencies:
    tslib: ^2.3.1
  checksum: 9f4f225a299962aaff701e3bcb920129e1a5e6be91195ab35d121da0369316effa491e53db2dc027f001122f6dfd44f9c5eca2cfbcd18341cdd5364cc0b84377
  languageName: node
  linkType: hard

"@docusaurus/utils-validation@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/utils-validation@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/logger": 2.0.0-beta.14
    "@docusaurus/utils": 2.0.0-beta.14
    joi: ^17.4.2
    tslib: ^2.3.1
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: f7ce474afe40c94cd9c82e02ee4814d4be507bf4947a793e34cd39146bff0415913646386e136b6d1a8aca5fe1621ba199cb3f8cb7e5d7a87da197cdfc58a5f7
  languageName: node
  linkType: hard

"@docusaurus/utils@npm:2.0.0-beta.14":
  version: 2.0.0-beta.14
  resolution: "@docusaurus/utils@npm:2.0.0-beta.14"
  dependencies:
    "@docusaurus/logger": 2.0.0-beta.14
    "@mdx-js/runtime": ^1.6.22
    "@svgr/webpack": ^6.0.0
    escape-string-regexp: ^4.0.0
    file-loader: ^6.2.0
    fs-extra: ^10.0.0
    github-slugger: ^1.4.0
    globby: ^11.0.4
    gray-matter: ^4.0.3
    lodash: ^4.17.20
    micromatch: ^4.0.4
    remark-mdx-remove-exports: ^1.6.22
    remark-mdx-remove-imports: ^1.6.22
    resolve-pathname: ^3.0.0
    tslib: ^2.3.1
    url-loader: ^4.1.1
  peerDependencies:
    react: "*"
    react-dom: "*"
    webpack: 5.x
  checksum: f527eeeca51ca8f38a7d2a486af4091ef59440ff9c98c0053327d003a806a65298007ac57b02dd55b87d4cca222af45aebce3be4f6d065f5ac3abcf78567c49e
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^0.8.8":
  version: 0.8.8
  resolution: "@emotion/is-prop-valid@npm:0.8.8"
  dependencies:
    "@emotion/memoize": 0.7.4
  checksum: bb7ec6d48c572c540e24e47cc94fc2f8dec2d6a342ae97bc9c8b6388d9b8d283862672172a1bb62d335c02662afe6291e10c71e9b8642664a8b43416cdceffac
  languageName: node
  linkType: hard

"@emotion/memoize@npm:0.7.4":
  version: 0.7.4
  resolution: "@emotion/memoize@npm:0.7.4"
  checksum: 4e3920d4ec95995657a37beb43d3f4b7d89fed6caa2b173a4c04d10482d089d5c3ea50bbc96618d918b020f26ed6e9c4026bbd45433566576c1f7b056c3271dc
  languageName: node
  linkType: hard

"@emotion/stylis@npm:^0.8.4":
  version: 0.8.5
  resolution: "@emotion/stylis@npm:0.8.5"
  checksum: 67ff5958449b2374b329fb96e83cb9025775ffe1e79153b499537c6c8b2eb64b77f32d7b5d004d646973662356ceb646afd9269001b97c54439fceea3203ce65
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.7.4":
  version: 0.7.5
  resolution: "@emotion/unitless@npm:0.7.5"
  checksum: f976e5345b53fae9414a7b2e7a949aa6b52f8bdbcc84458b1ddc0729e77ba1d1dfdff9960e0da60183877873d3a631fa24d9695dd714ed94bcd3ba5196586a6b
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.0.1":
  version: 1.1.2
  resolution: "@gar/promisify@npm:1.1.2"
  checksum: d05081e0887a49c178b75ee3067bd6ee086f73c154d121b854fb2e044e8a89cb1cbb6de3a0dd93a519b80f0531fda68b099dd7256205f7fbb3490324342f2217
  languageName: node
  linkType: hard

"@hapi/hoek@npm:^9.0.0":
  version: 9.2.1
  resolution: "@hapi/hoek@npm:9.2.1"
  checksum: 6a439f672df5f12f1d08d56967b4cb364ce05d81e95e3c3c1b88c5a98b917ca91c70e78cc0b2b4219a760cceec1f22d6658bfc93a83670cecc1ce9ca2247ebd8
  languageName: node
  linkType: hard

"@hapi/topo@npm:^5.0.0":
  version: 5.1.0
  resolution: "@hapi/topo@npm:5.1.0"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: 604dfd5dde76d5c334bd03f9001fce69c7ce529883acf92da96f4fe7e51221bf5e5110e964caca287a6a616ba027c071748ab636ff178ad750547fba611d6014
  languageName: node
  linkType: hard

"@mdx-js/mdx@npm:1.6.22, @mdx-js/mdx@npm:^1.6.21":
  version: 1.6.22
  resolution: "@mdx-js/mdx@npm:1.6.22"
  dependencies:
    "@babel/core": 7.12.9
    "@babel/plugin-syntax-jsx": 7.12.1
    "@babel/plugin-syntax-object-rest-spread": 7.8.3
    "@mdx-js/util": 1.6.22
    babel-plugin-apply-mdx-type-prop: 1.6.22
    babel-plugin-extract-import-names: 1.6.22
    camelcase-css: 2.0.1
    detab: 2.0.4
    hast-util-raw: 6.0.1
    lodash.uniq: 4.5.0
    mdast-util-to-hast: 10.0.1
    remark-footnotes: 2.0.0
    remark-mdx: 1.6.22
    remark-parse: 8.0.3
    remark-squeeze-paragraphs: 4.0.0
    style-to-object: 0.3.0
    unified: 9.2.0
    unist-builder: 2.0.3
    unist-util-visit: 2.0.3
  checksum: 0839b4a3899416326ea6578fe9e470af319da559bc6d3669c60942e456b49a98eebeb3358c623007b4786a2175a450d2c51cd59df64639013c5a3d22366931a6
  languageName: node
  linkType: hard

"@mdx-js/react@npm:1.6.22, @mdx-js/react@npm:^1.6.21":
  version: 1.6.22
  resolution: "@mdx-js/react@npm:1.6.22"
  peerDependencies:
    react: ^16.13.1 || ^17.0.0
  checksum: bc84bd514bc127f898819a0c6f1a6915d9541011bd8aefa1fcc1c9bea8939f31051409e546bdec92babfa5b56092a16d05ef6d318304ac029299df5181dc94c8
  languageName: node
  linkType: hard

"@mdx-js/runtime@npm:^1.6.22":
  version: 1.6.22
  resolution: "@mdx-js/runtime@npm:1.6.22"
  dependencies:
    "@mdx-js/mdx": 1.6.22
    "@mdx-js/react": 1.6.22
    buble-jsx-only: ^0.19.8
  peerDependencies:
    react: ^16.13.1
  checksum: 28f881891ecb514c0ae774cfae0e1a9ed84206b8797a9f961a694a79a69d0c3c312e2030934c19f350e1da36a8a0c07a7d5988520b2e4fc8505e53b3b7f39473
  languageName: node
  linkType: hard

"@mdx-js/util@npm:1.6.22":
  version: 1.6.22
  resolution: "@mdx-js/util@npm:1.6.22"
  checksum: 4b393907e39a1a75214f0314bf72a0adfa5e5adffd050dd5efe9c055b8549481a3cfc9f308c16dfb33311daf3ff63added7d5fd1fe52db614c004f886e0e559a
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^1.0.0":
  version: 1.1.0
  resolution: "@npmcli/fs@npm:1.1.0"
  dependencies:
    "@gar/promisify": ^1.0.1
    semver: ^7.3.5
  checksum: e435b883b4f8da8c95a820f458cabb7d86582406eed5ad79fc689000d3e2df17e1f475c4903627272c001357cabc70d8b4c62520cbdae8cfab1dfdd51949f408
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^1.0.1":
  version: 1.1.2
  resolution: "@npmcli/move-file@npm:1.1.2"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: c96381d4a37448ea280951e46233f7e541058cf57a57d4094dd4bdcaae43fa5872b5f2eb6bfb004591a68e29c5877abe3cdc210cb3588cbf20ab2877f31a7de7
  languageName: node
  linkType: hard

"@philpl/buble@npm:^0.19.7":
  version: 0.19.7
  resolution: "@philpl/buble@npm:0.19.7"
  dependencies:
    acorn: ^6.1.1
    acorn-class-fields: ^0.2.1
    acorn-dynamic-import: ^4.0.0
    acorn-jsx: ^5.0.1
    chalk: ^2.4.2
    magic-string: ^0.25.2
    minimist: ^1.2.0
    os-homedir: ^1.0.1
    regexpu-core: ^4.5.4
  bin:
    buble: ./bin/buble
  checksum: 74d71f9f9c5125dfed966415c28b0a55f5b7256248774d3397b499d3f4e20fdf99f77628a76c3250e28ff2d3d4cf2b76fc93a1bc1c14c2597ff0a3ec83fd211a
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.20":
  version: 1.0.0-next.21
  resolution: "@polka/url@npm:1.0.0-next.21"
  checksum: c7654046d38984257dd639eab3dc770d1b0340916097b2fac03ce5d23506ada684e05574a69b255c32ea6a144a957c8cd84264159b545fca031c772289d88788
  languageName: node
  linkType: hard

"@rollup/plugin-babel@npm:^5.2.0":
  version: 5.3.0
  resolution: "@rollup/plugin-babel@npm:5.3.0"
  dependencies:
    "@babel/helper-module-imports": ^7.10.4
    "@rollup/pluginutils": ^3.1.0
  peerDependencies:
    "@babel/core": ^7.0.0
    "@types/babel__core": ^7.1.9
    rollup: ^1.20.0||^2.0.0
  peerDependenciesMeta:
    "@types/babel__core":
      optional: true
  checksum: 6cfd741790f125968cbd0fc91b6f54e235033e31853a12190f725ccf95a6eb2f1387b6368be80dedfa94536d2e84739e7af45c8b2fe7a450e91c2aeb6170867d
  languageName: node
  linkType: hard

"@rollup/plugin-node-resolve@npm:^11.2.1":
  version: 11.2.1
  resolution: "@rollup/plugin-node-resolve@npm:11.2.1"
  dependencies:
    "@rollup/pluginutils": ^3.1.0
    "@types/resolve": 1.17.1
    builtin-modules: ^3.1.0
    deepmerge: ^4.2.2
    is-module: ^1.0.0
    resolve: ^1.19.0
  peerDependencies:
    rollup: ^1.20.0||^2.0.0
  checksum: 6f3b3ecf9a0596a5db4212984bdeb13bb7612693602407e9457ada075dea5a5f2e4e124c592352cf27066a88b194de9b9a95390149b52cf335d5b5e17b4e265b
  languageName: node
  linkType: hard

"@rollup/plugin-replace@npm:^2.4.1":
  version: 2.4.2
  resolution: "@rollup/plugin-replace@npm:2.4.2"
  dependencies:
    "@rollup/pluginutils": ^3.1.0
    magic-string: ^0.25.7
  peerDependencies:
    rollup: ^1.20.0 || ^2.0.0
  checksum: b2f1618ee5526d288e2f8ae328dcb326e20e8dc8bd1f60d3e14d6708a5832e4aa44811f7d493f4aed2deeadca86e3b6b0503cd39bf50cfb4b595bb9da027fad0
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^3.1.0":
  version: 3.1.0
  resolution: "@rollup/pluginutils@npm:3.1.0"
  dependencies:
    "@types/estree": 0.0.39
    estree-walker: ^1.0.1
    picomatch: ^2.2.2
  peerDependencies:
    rollup: ^1.20.0||^2.0.0
  checksum: 8be16e27863c219edbb25a4e6ec2fe0e1e451d9e917b6a43cf2ae5bc025a6b8faaa40f82a6e53b66d0de37b58ff472c6c3d57a83037ae635041f8df959d6d9aa
  languageName: node
  linkType: hard

"@sideway/address@npm:^4.1.3":
  version: 4.1.3
  resolution: "@sideway/address@npm:4.1.3"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: 3c1faf6ef37a0b59b62ce42b59c012c00ef1fc4194ad6776c65c2f9a6dd6c1710c6f6362b3ca3fa582fdb93984f0cb64ca44f9f5e02940634805f5e561279c22
  languageName: node
  linkType: hard

"@sideway/formula@npm:^3.0.0":
  version: 3.0.0
  resolution: "@sideway/formula@npm:3.0.0"
  checksum: 8ae26a0ed6bc84f7310be6aae6eb9d81e97f382619fc69025d346871a707eaab0fa38b8c857e3f0c35a19923de129f42d35c50b8010c928d64aab41578580ec4
  languageName: node
  linkType: hard

"@sideway/pinpoint@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sideway/pinpoint@npm:2.0.0"
  checksum: 0f4491e5897fcf5bf02c46f5c359c56a314e90ba243f42f0c100437935daa2488f20482f0f77186bd6bf43345095a95d8143ecf8b1f4d876a7bc0806aba9c3d2
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^0.14.0":
  version: 0.14.0
  resolution: "@sindresorhus/is@npm:0.14.0"
  checksum: 971e0441dd44ba3909b467219a5e242da0fc584048db5324cfb8048148fa8dcc9d44d71e3948972c4f6121d24e5da402ef191420d1266a95f713bb6d6e59c98a
  languageName: node
  linkType: hard

"@slorber/static-site-generator-webpack-plugin@npm:^4.0.0":
  version: 4.0.1
  resolution: "@slorber/static-site-generator-webpack-plugin@npm:4.0.1"
  dependencies:
    bluebird: ^3.7.1
    cheerio: ^0.22.0
    eval: ^0.1.4
    url: ^0.11.0
    webpack-sources: ^1.4.3
  checksum: 6ddf70513c869d23044abf74574392c4c98f8a79bcd29a2a07e06b8aca8945dab48eb48a61d7bae7217fe7d9b9ce16abe50fe4b8e75bfa35b4aae4d16c637fd3
  languageName: node
  linkType: hard

"@surma/rollup-plugin-off-main-thread@npm:^2.2.3":
  version: 2.2.3
  resolution: "@surma/rollup-plugin-off-main-thread@npm:2.2.3"
  dependencies:
    ejs: ^3.1.6
    json5: ^2.2.0
    magic-string: ^0.25.0
    string.prototype.matchall: ^4.0.6
  checksum: 2c021349442e2e2cec96bb50fd82ec8bf8514d909bc73594f6cfc89b3b68f2feed909a8161d7d307d9455585c97e6b66853ce334db432626c7596836d4549c0c
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8200dfa2ee903a42a376fec73feb591414cced5674dbff646be85bf6f3587ff74ecbaffa14e2cc096d0b3325630d30872c3f350a8ac501e6672a8e7b1ff3e0f5
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 82c988ed40f88640fcd68fc24ff3dbf729673d59cf1627ed0aa5f0c992a1ddc220fe23e7f23ba39110cd47720cc7c630e70333f1a25ff6a65662584317ff2385
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c80e3ff4082ebb4aa07a6bc115d98c320c3f69dc9b74c22552084ca9043cd87f8dcc3b7fd40950433d0325848427446d7aadba979f84867b3e35ef0271483866
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d6b5e5a9834caf3e08c286843185a6ebde90c1223be09d789a6aaf30d75a18a77ee8672b3182f1c5b585e123c2b45e80dd1304e69e62272818ef0b00599c57aa
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b62e0eb16d056545f86aaa3f97928c82de619dbbe2de879e7c6c4d9436d5bd86fa11de3f3e309ab69c4ca37d5cf293b11de6e8e81e302ea5fb5121fb0564b643
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 873c6ef439064f18c68b652fa21bab94668d5647a545146fc24dad82141a9d455fd969e3d89357ae60db6caaec9fbd9253dabddadde095a36eee1e21f6060611
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:6.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 29df306ce059ed01e30cdcda9684d3b8bbb9513bfd0c257dc351d54ef6472b2ed0de2766f60acacde38bcc84dffd995f08b354308e20b8fc982234530ce1eeab
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:^6.2.0":
  version: 6.2.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:6.2.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2d4c4ff27c65d26dc4e6fbbdb85ab1fce701473c0616a7f0a55a671f530c4ad3a56e21c627c4b649b592bb9731fc7238f2c39871bc27a8e090dce8b751b1f9d5
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:^6.2.0":
  version: 6.2.0
  resolution: "@svgr/babel-preset@npm:6.2.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": ^6.0.0
    "@svgr/babel-plugin-remove-jsx-attribute": ^6.0.0
    "@svgr/babel-plugin-remove-jsx-empty-expression": ^6.0.0
    "@svgr/babel-plugin-replace-jsx-attribute-value": ^6.0.0
    "@svgr/babel-plugin-svg-dynamic-title": ^6.0.0
    "@svgr/babel-plugin-svg-em-dimensions": ^6.0.0
    "@svgr/babel-plugin-transform-react-native-svg": ^6.0.0
    "@svgr/babel-plugin-transform-svg-component": ^6.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9a5ce414815df2c5f05add8a322ce42182563198a8d379850834d801fda3319eed5a3f7f1174c5163626dd9f8f4af36cad7049b0603c8de21e1bc859b931bcea
  languageName: node
  linkType: hard

"@svgr/core@npm:^6.2.0":
  version: 6.2.0
  resolution: "@svgr/core@npm:6.2.0"
  dependencies:
    "@svgr/plugin-jsx": ^6.2.0
    camelcase: ^6.2.0
    cosmiconfig: ^7.0.1
  checksum: 02c7ed5a5ebf1fad03b8e2c844dbb10077e2a6c2f53cebd751397784c6725ae37e426e51c4d47f51fc7eeb9c9bf1ae469b7b60ddf3081709870ed91dfd4df6f1
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:^6.0.0":
  version: 6.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:6.0.0"
  dependencies:
    "@babel/types": ^7.15.6
    entities: ^3.0.1
  checksum: cf660c9c5a4cab056cf914f1f7d33176f196d9dae37fbdc4a0dedfc4c8ed79472f209cc885e46d94338808cd00a479d50c3b27c5c925a1b037217eb36b13b7e0
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:^6.2.0":
  version: 6.2.0
  resolution: "@svgr/plugin-jsx@npm:6.2.0"
  dependencies:
    "@babel/core": ^7.15.5
    "@svgr/babel-preset": ^6.2.0
    "@svgr/hast-util-to-babel-ast": ^6.0.0
    svg-parser: ^2.0.2
  peerDependencies:
    "@svgr/core": ^6.0.0
  checksum: 7f0482603a18d0cda57d66a0449e332394aac9b5b6a852db182943bc5c3a5388f90a1c4b47f4d2c6147ebc6c9f51bebc29bcc2152a2feac87d7ec969baf8164e
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:^6.2.0":
  version: 6.2.0
  resolution: "@svgr/plugin-svgo@npm:6.2.0"
  dependencies:
    cosmiconfig: ^7.0.1
    deepmerge: ^4.2.2
    svgo: ^2.5.0
  peerDependencies:
    "@svgr/core": ^6.0.0
  checksum: 74d3aedd0fcaafbfe4985924b4d40e63536a686988eff52a3411cf83851ce2afc1f5e84e203dae18ab896db48c0b824dcfb8c5dd5b071b4ea90d00fc08951254
  languageName: node
  linkType: hard

"@svgr/webpack@npm:^6.0.0":
  version: 6.2.0
  resolution: "@svgr/webpack@npm:6.2.0"
  dependencies:
    "@babel/core": ^7.15.5
    "@babel/plugin-transform-react-constant-elements": ^7.14.5
    "@babel/preset-env": ^7.15.6
    "@babel/preset-react": ^7.14.5
    "@babel/preset-typescript": ^7.15.0
    "@svgr/core": ^6.2.0
    "@svgr/plugin-jsx": ^6.2.0
    "@svgr/plugin-svgo": ^6.2.0
  checksum: 4fae0ee56c0674a72d896bd3d0ad7f188eb7320f060db1467953f323055c7f75e2f3b827dcf1670a2888209fa4ef30f42c1d4f89db37ff90e3c1622e9b04bf3b
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^1.1.2":
  version: 1.1.2
  resolution: "@szmarczak/http-timer@npm:1.1.2"
  dependencies:
    defer-to-connect: ^1.0.1
  checksum: 4d9158061c5f397c57b4988cde33a163244e4f02df16364f103971957a32886beb104d6180902cbe8b38cb940e234d9f98a4e486200deca621923f62f50a06fe
  languageName: node
  linkType: hard

"@tootallnate/once@npm:1":
  version: 1.1.2
  resolution: "@tootallnate/once@npm:1.1.2"
  checksum: e1fb1bbbc12089a0cb9433dc290f97bddd062deadb6178ce9bcb93bb7c1aecde5e60184bc7065aec42fe1663622a213493c48bbd4972d931aae48315f18e1be9
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@tsconfig/docusaurus@npm:^1.0.4":
  version: 1.0.4
  resolution: "@tsconfig/docusaurus@npm:1.0.4"
  checksum: 720ca04baa47cbd7ec9c4f6ce18c5fcc2117a077ceff9fe89b7ac66be4968a9ea37bbf8c72e91e517ed57953b38ce6d1c7e05380623f2c579ae4021fb59f0632
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.2
  resolution: "@types/body-parser@npm:1.19.2"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: e17840c7d747a549f00aebe72c89313d09fbc4b632b949b2470c5cb3b1cb73863901ae84d9335b567a79ec5efcfb8a28ff8e3f36bc8748a9686756b6d5681f40
  languageName: node
  linkType: hard

"@types/bonjour@npm:^3.5.9":
  version: 3.5.10
  resolution: "@types/bonjour@npm:3.5.10"
  dependencies:
    "@types/node": "*"
  checksum: bfcadb042a41b124c4e3de4925e3be6d35b78f93f27c4535d5ff86980dc0f8bc407ed99b9b54528952dc62834d5a779392f7a12c2947dd19330eb05a6bcae15a
  languageName: node
  linkType: hard

"@types/connect-history-api-fallback@npm:^1.3.5":
  version: 1.3.5
  resolution: "@types/connect-history-api-fallback@npm:1.3.5"
  dependencies:
    "@types/express-serve-static-core": "*"
    "@types/node": "*"
  checksum: 464d06e5ab00f113fa89978633d5eb00d225aeb4ebbadc07f6f3bc337aa7cbfcd74957b2a539d6d47f2e128e956a17819973ec7ae62ade2e16e367a6c38b8d3a
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.35
  resolution: "@types/connect@npm:3.4.35"
  dependencies:
    "@types/node": "*"
  checksum: fe81351470f2d3165e8b12ce33542eef89ea893e36dd62e8f7d72566dfb7e448376ae962f9f3ea888547ce8b55a40020ca0e01d637fab5d99567673084542641
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.0":
  version: 3.7.3
  resolution: "@types/eslint-scope@npm:3.7.3"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: 6772b05e1b92003d1f295e81bc847a61f4fbe8ddab77ffa49e84ed3f9552513bdde677eb53ef167753901282857dd1d604d9f82eddb34a233495932b2dc3dc17
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 8.2.2
  resolution: "@types/eslint@npm:8.2.2"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: acbbaecea2675b63cc4b067df499bb15906a56379a3603a9f6afffe2eb688b30bb73b1f5a402e44de36c5dc76abf59027a9f557b171d0b544ad01fa333118b6b
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^0.0.50":
  version: 0.0.50
  resolution: "@types/estree@npm:0.0.50"
  checksum: 9a2b6a4a8c117f34d08fbda5e8f69b1dfb109f7d149b60b00fd7a9fb6ac545c078bc590aa4ec2f0a256d680cf72c88b3b28b60c326ee38a7bc8ee1ee95624922
  languageName: node
  linkType: hard

"@types/estree@npm:0.0.39":
  version: 0.0.39
  resolution: "@types/estree@npm:0.0.39"
  checksum: 412fb5b9868f2c418126451821833414189b75cc6bf84361156feed733e3d92ec220b9d74a89e52722e03d5e241b2932732711b7497374a404fad49087adc248
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:*, @types/express-serve-static-core@npm:^4.17.18":
  version: 4.17.28
  resolution: "@types/express-serve-static-core@npm:4.17.28"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
  checksum: 826489811a5b371c10f02443b4ca894ffc05813bfdf2b60c224f5c18ac9a30a2e518cb9ef9fdfcaa2a1bb17f8bfa4ed1859ccdb252e879c9276271b4ee2df5a9
  languageName: node
  linkType: hard

"@types/express@npm:*":
  version: 4.17.13
  resolution: "@types/express@npm:4.17.13"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^4.17.18
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: 12a2a0e6c4b993fc0854bec665906788aea0d8ee4392389d7a98a5de1eefdd33c9e1e40a91f3afd274011119c506f7b4126acb97fae62ae20b654974d44cba12
  languageName: node
  linkType: hard

"@types/hast@npm:^2.0.0":
  version: 2.3.4
  resolution: "@types/hast@npm:2.3.4"
  dependencies:
    "@types/unist": "*"
  checksum: fff47998f4c11e21a7454b58673f70478740ecdafd95aaf50b70a3daa7da9cdc57315545bf9c039613732c40b7b0e9e49d11d03fe9a4304721cdc3b29a88141e
  languageName: node
  linkType: hard

"@types/html-minifier-terser@npm:^6.0.0":
  version: 6.1.0
  resolution: "@types/html-minifier-terser@npm:6.1.0"
  checksum: eb843f6a8d662d44fb18ec61041117734c6aae77aa38df1be3b4712e8e50ffaa35f1e1c92fdd0fde14a5675fecf457abcd0d15a01fae7506c91926176967f452
  languageName: node
  linkType: hard

"@types/http-proxy@npm:^1.17.5":
  version: 1.17.8
  resolution: "@types/http-proxy@npm:1.17.8"
  dependencies:
    "@types/node": "*"
  checksum: 3b3d683498267096c8aca03652702243b1e087bc20e77a9abe74fdbee1c89c8283ee41c47d245cda2f422483b01980d70a1030b92a8ff24b280e0aa868241a8b
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.4, @types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.9
  resolution: "@types/json-schema@npm:7.0.9"
  checksum: 259d0e25f11a21ba5c708f7ea47196bd396e379fddb79c76f9f4f62c945879dc21657904914313ec2754e443c5018ea8372362f323f30e0792897fdb2098a705
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.1":
  version: 3.1.3
  resolution: "@types/keyv@npm:3.1.3"
  dependencies:
    "@types/node": "*"
  checksum: b5f8aa592cc21c16d99e69aec0976f12b893b055e4456d90148a610a6b6088e297b2ba5f38f8c8280cef006cfd8f9ec99e069905020882619dc5fc8aa46f5f27
  languageName: node
  linkType: hard

"@types/mdast@npm:^3.0.0":
  version: 3.0.10
  resolution: "@types/mdast@npm:3.0.10"
  dependencies:
    "@types/unist": "*"
  checksum: 3f587bfc0a9a2403ecadc220e61031b01734fedaf82e27eb4d5ba039c0eb54db8c85681ccc070ab4df3f7ec711b736a82b990e69caa14c74bf7ac0ccf2ac7313
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.2
  resolution: "@types/mime@npm:1.3.2"
  checksum: 0493368244cced1a69cb791b485a260a422e6fcc857782e1178d1e6f219f1b161793e9f87f5fae1b219af0f50bee24fcbe733a18b4be8fdd07a38a8fb91146fd
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^17.0.5":
  version: 17.0.8
  resolution: "@types/node@npm:17.0.8"
  checksum: f4cadeb9e602027520abc88c77142697e33cf6ac98bb02f8b595a398603cbd33df1f94d01c055c9f13cde0c8eaafc5e396ca72645458d42b4318b845bc7f1d0f
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "@types/parse-json@npm:4.0.0"
  checksum: fd6bce2b674b6efc3db4c7c3d336bd70c90838e8439de639b909ce22f3720d21344f52427f1d9e57b265fcb7f6c018699b99e5e0c208a1a4823014269a6bf35b
  languageName: node
  linkType: hard

"@types/parse5@npm:^5.0.0":
  version: 5.0.3
  resolution: "@types/parse5@npm:5.0.3"
  checksum: d6b7495cb1850f9f2e9c5e103ede9f2d30a5320669707b105c403868adc9e4bf8d3a7ff314cc23f67826bbbbbc0e6147346ce9062ab429f099dba7a01f463919
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.4
  resolution: "@types/prop-types@npm:15.7.4"
  checksum: ef6e1899e59b876c273811b1bd845022fc66d5a3d11cb38a25b6c566b30514ae38fe20a40f67622f362a4f4f7f9224e22d8da101cff3d6e97e11d7b4c307cfc1
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.7
  resolution: "@types/qs@npm:6.9.7"
  checksum: 7fd6f9c25053e9b5bb6bc9f9f76c1d89e6c04f7707a7ba0e44cc01f17ef5284adb82f230f542c2d5557d69407c9a40f0f3515e8319afd14e1e16b5543ac6cdba
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.4
  resolution: "@types/range-parser@npm:1.2.4"
  checksum: b7c0dfd5080a989d6c8bb0b6750fc0933d9acabeb476da6fe71d8bdf1ab65e37c136169d84148034802f48378ab94e3c37bb4ef7656b2bec2cb9c0f8d4146a95
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 17.0.38
  resolution: "@types/react@npm:17.0.38"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: 4079f4f959cd4a4bfaeda8b89fe8a1b1f8bdc9d87acfdc5f74a0b39cec9ec6a470724357c62778c0f063180b360c250e920c5a142f1dbcda67d9cc25a6d43a85
  languageName: node
  linkType: hard

"@types/resolve@npm:1.17.1":
  version: 1.17.1
  resolution: "@types/resolve@npm:1.17.1"
  dependencies:
    "@types/node": "*"
  checksum: dc6a6df507656004e242dcb02c784479deca516d5f4b58a1707e708022b269ae147e1da0521f3e8ad0d63638869d87e0adc023f0bd5454aa6f72ac66c7525cf5
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.0
  resolution: "@types/responselike@npm:1.0.0"
  dependencies:
    "@types/node": "*"
  checksum: e99fc7cc6265407987b30deda54c1c24bb1478803faf6037557a774b2f034c5b097ffd65847daa87e82a61a250d919f35c3588654b0fdaa816906650f596d1b0
  languageName: node
  linkType: hard

"@types/retry@npm:^0.12.0":
  version: 0.12.1
  resolution: "@types/retry@npm:0.12.1"
  checksum: 5f46b2556053655f78262bb33040dc58417c900457cc63ff37d6c35349814471453ef511af0cec76a540c601296cd2b22f64bab1ab649c0dacc0223765ba876c
  languageName: node
  linkType: hard

"@types/sax@npm:^1.2.1":
  version: 1.2.4
  resolution: "@types/sax@npm:1.2.4"
  dependencies:
    "@types/node": "*"
  checksum: 2aa50cbf1d1f0cf8541ef1787f94c7442e58e63900afd3b45c354e4140ed5efc5cf26fca8eb9df9970a74c7ea582293ae2083271bd046dedf4c3cc2689a40892
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.2
  resolution: "@types/scheduler@npm:0.16.2"
  checksum: b6b4dcfeae6deba2e06a70941860fb1435730576d3689225a421280b7742318d1548b3d22c1f66ab68e414f346a9542f29240bc955b6332c5b11e561077583bc
  languageName: node
  linkType: hard

"@types/serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "@types/serve-index@npm:1.9.1"
  dependencies:
    "@types/express": "*"
  checksum: 026f3995fb500f6df7c3fe5009e53bad6d739e20b84089f58ebfafb2f404bbbb6162bbe33f72d2f2af32d5b8d3799c8e179793f90d9ed5871fb8591190bb6056
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.13.10
  resolution: "@types/serve-static@npm:1.13.10"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: eaca858739483e3ded254cad7d7a679dc2c8b3f52c8bb0cd845b3b7eb1984bde0371fdcb0a5c83aa12e6daf61b6beb762545021f520f08a1fe882a3fa4ea5554
  languageName: node
  linkType: hard

"@types/sockjs@npm:^0.3.33":
  version: 0.3.33
  resolution: "@types/sockjs@npm:0.3.33"
  dependencies:
    "@types/node": "*"
  checksum: b9bbb2b5c5ead2fb884bb019f61a014e37410bddd295de28184e1b2e71ee6b04120c5ba7b9954617f0bdf962c13d06249ce65004490889c747c80d3f628ea842
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.2":
  version: 2.0.2
  resolution: "@types/trusted-types@npm:2.0.2"
  checksum: 3371eef5f1c50e1c3c07a127c1207b262ba65b83dd167a1c460fc1b135a3fb0c97b9f508efebd383f239cc5dd5b7169093686a692a501fde9c3f7208657d9b0d
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^2.0.0, @types/unist@npm:^2.0.2, @types/unist@npm:^2.0.3":
  version: 2.0.6
  resolution: "@types/unist@npm:2.0.6"
  checksum: 25cb860ff10dde48b54622d58b23e66214211a61c84c0f15f88d38b61aa1b53d4d46e42b557924a93178c501c166aa37e28d7f6d994aba13d24685326272d5db
  languageName: node
  linkType: hard

"@types/ws@npm:^8.2.2":
  version: 8.2.2
  resolution: "@types/ws@npm:8.2.2"
  dependencies:
    "@types/node": "*"
  checksum: 308957864b9a5a0378ac82f1b084fa31b1bbe85106fb0d84ed2b392e4829404f21ab6ab2c1eb782d556e59cd33d57c75ad2d0cedc4b9b9d0ca3b2311bc915578
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ast@npm:1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
  checksum: 1eee1534adebeece635362f8e834ae03e389281972611408d64be7895fc49f48f98fddbbb5339bf8a72cb101bcb066e8bca3ca1bf1ef47dadf89def0395a8d87
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.1"
  checksum: b8efc6fa08e4787b7f8e682182d84dfdf8da9d9c77cae5d293818bc4a55c1f419a87fa265ab85252b3e6c1fd323d799efea68d825d341a7c365c64bc14750e97
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.1"
  checksum: 0792813f0ed4a0e5ee0750e8b5d0c631f08e927f4bdfdd9fe9105dc410c786850b8c61bff7f9f515fdfb149903bec3c976a1310573a4c6866a94d49bc7271959
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.11.1"
  checksum: a337ee44b45590c3a30db5a8b7b68a717526cf967ada9f10253995294dbd70a58b2da2165222e0b9830cd4fc6e4c833bf441a721128d1fe2e9a7ab26b36003ce
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: 44d2905dac2f14d1e9b5765cf1063a0fa3d57295c6d8930f6c59a36462afecc6e763e8a110b97b342a0f13376166c5d41aa928e6ced92e2f06b071fd0db59d3a
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.1"
  checksum: eac400113127832c88f5826bcc3ad1c0db9b3dbd4c51a723cfdb16af6bfcbceb608170fdaac0ab7731a7e18b291be7af68a47fcdb41cfe0260c10857e7413d97
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
  checksum: 617696cfe8ecaf0532763162aaf748eb69096fb27950219bb87686c6b2e66e11cd0614d95d319d0ab1904bc14ebe4e29068b12c3e7c5e020281379741fe4bedf
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ieee754@npm:1.11.1"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 23a0ac02a50f244471631802798a816524df17e56b1ef929f0c73e3cde70eaf105a24130105c60aff9d64a24ce3b640dad443d6f86e5967f922943a7115022ec
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/leb128@npm:1.11.1"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 33ccc4ade2f24de07bf31690844d0b1ad224304ee2062b0e464a610b0209c79e0b3009ac190efe0e6bd568b0d1578d7c3047fc1f9d0197c92fc061f56224ff4a
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/utf8@npm:1.11.1"
  checksum: 972c5cfc769d7af79313a6bfb96517253a270a4bf0c33ba486aa43cac43917184fb35e51dfc9e6b5601548cd5931479a42e42c89a13bb591ffabebf30c8a6a0b
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/helper-wasm-section": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-opt": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    "@webassemblyjs/wast-printer": 1.11.1
  checksum: 6d7d9efaec1227e7ef7585a5d7ff0be5f329f7c1c6b6c0e906b18ed2e9a28792a5635e450aca2d136770d0207225f204eff70a4b8fd879d3ac79e1dcc26dbeb9
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 1f6921e640293bf99fb16b21e09acb59b340a79f986c8f979853a0ae9f0b58557534b81e02ea2b4ef11e929d946708533fd0693c7f3712924128fdafd6465f5b
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
  checksum: 21586883a20009e2b20feb67bdc451bbc6942252e038aae4c3a08e6f67b6bae0f5f88f20bfc7bd0452db5000bacaf5ab42b98cf9aa034a6c70e9fc616142e1db
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 1521644065c360e7b27fad9f4bb2df1802d134dd62937fa1f601a1975cde56bc31a57b6e26408b9ee0228626ff3ba1131ae6f74ffb7d718415b6528c5a6dbfc2
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wast-printer@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: f15ae4c2441b979a3b4fce78f3d83472fb22350c6dc3fd34bfe7c3da108e0b2360718734d961bba20e7716cb8578e964b870da55b035e209e50ec9db0378a3f7
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.5, accepts@npm:~1.3.7":
  version: 1.3.7
  resolution: "accepts@npm:1.3.7"
  dependencies:
    mime-types: ~2.1.24
    negotiator: 0.6.2
  checksum: 27fc8060ffc69481ff6719cd3ee06387d2b88381cb0ce626f087781bbd02201a645a9febc8e7e7333558354b33b1d2f922ad13560be4ec1b7ba9e76fc1c1241d
  languageName: node
  linkType: hard

"acorn-class-fields@npm:^0.2.1":
  version: 0.2.1
  resolution: "acorn-class-fields@npm:0.2.1"
  peerDependencies:
    acorn: ^6.0.0
  checksum: 94e46e7bc692a75df690b3904965869a3c66a2e5c20f94d8e97f605418a5ff05fd1f706d1c91f892d1f88526c2d1c78b8c8c5dcb5d247f866b3f863a4bb0b37f
  languageName: node
  linkType: hard

"acorn-dynamic-import@npm:^4.0.0":
  version: 4.0.0
  resolution: "acorn-dynamic-import@npm:4.0.0"
  peerDependencies:
    acorn: ^6.0.0
  checksum: ef7298e632e9d107b2be06b47d607de94d7213ca2417fced02af76b0c71e13074d98924e270c7bfec421c1049ed9001a97ed4d0f28020d9cfa1aae16ca20664a
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.7.6":
  version: 1.8.0
  resolution: "acorn-import-assertions@npm:1.8.0"
  peerDependencies:
    acorn: ^8
  checksum: 5c4cf7c850102ba7ae0eeae0deb40fb3158c8ca5ff15c0bca43b5c47e307a1de3d8ef761788f881343680ea374631ae9e9615ba8876fee5268dbe068c98bcba6
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.0.1":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^6.1.1":
  version: 6.4.2
  resolution: "acorn@npm:6.4.2"
  bin:
    acorn: bin/acorn
  checksum: 44b07053729db7f44d28343eed32247ed56dc4a6ec6dff2b743141ecd6b861406bbc1c20bf9d4f143ea7dd08add5dc8c290582756539bc03a8db605050ce2fb4
  languageName: node
  linkType: hard

"acorn@npm:^8.0.4, acorn@npm:^8.4.1, acorn@npm:^8.7.0":
  version: 8.7.0
  resolution: "acorn@npm:8.7.0"
  bin:
    acorn: bin/acorn
  checksum: e0f79409d68923fbf1aa6d4166f3eedc47955320d25c89a20cc822e6ba7c48c5963d5bc657bc242d68f7a4ac9faf96eef033e8f73656da6c640d4219935fdfd0
  languageName: node
  linkType: hard

"address@npm:^1.0.1, address@npm:^1.1.2":
  version: 1.1.2
  resolution: "address@npm:1.1.2"
  checksum: d966deee6ab9a0f96ed1d25dc73e91a248f64479c91f9daeb15237b8e3c39a02faac4e6afe8987ef9e5aea60a1593cef5882b7456ab2e6196fc0229a93ec39c2
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.3":
  version: 4.2.0
  resolution: "agentkeepalive@npm:4.2.0"
  dependencies:
    debug: ^4.1.0
    depd: ^1.1.2
    humanize-ms: ^1.2.1
  checksum: 89806f83ceebbcaabf6bd581a8dce4870910fd2a11f66df8f505b4cd4ce4ca5ab9e6eec8d11ce8531a6b60f6748b75b0775e0e2fa33871503ef00d535418a19a
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.4.1, ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.0.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:^6.12.2, ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.6.0, ajv@npm:^8.8.0":
  version: 8.8.2
  resolution: "ajv@npm:8.8.2"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: 90849ef03c4f4f7051d15f655120137b89e3205537d683beebd39d95f40c0ca00ea8476cd999602d2f433863e7e4bf1b81d1869d1e07f4dcf56d71b6430a605c
  languageName: node
  linkType: hard

"algoliasearch-helper@npm:^3.5.5":
  version: 3.7.0
  resolution: "algoliasearch-helper@npm:3.7.0"
  dependencies:
    "@algolia/events": ^4.0.1
  peerDependencies:
    algoliasearch: ">= 3.1 < 5"
  checksum: 34afebf5aa6db2f032b6e8aab7e3a3bdd062ea2eebd9120000dbc02367126ac99e7402ea3cb4e8cdff58df0661805adc52219fd932ed4435fb1058b8e78a61d4
  languageName: node
  linkType: hard

"algoliasearch@npm:^4.0.0, algoliasearch@npm:^4.10.5":
  version: 4.12.0
  resolution: "algoliasearch@npm:4.12.0"
  dependencies:
    "@algolia/cache-browser-local-storage": 4.12.0
    "@algolia/cache-common": 4.12.0
    "@algolia/cache-in-memory": 4.12.0
    "@algolia/client-account": 4.12.0
    "@algolia/client-analytics": 4.12.0
    "@algolia/client-common": 4.12.0
    "@algolia/client-personalization": 4.12.0
    "@algolia/client-search": 4.12.0
    "@algolia/logger-common": 4.12.0
    "@algolia/logger-console": 4.12.0
    "@algolia/requester-browser-xhr": 4.12.0
    "@algolia/requester-common": 4.12.0
    "@algolia/requester-node-http": 4.12.0
    "@algolia/transporter": 4.12.0
  checksum: 44054d9cf7869b0e87647c079a9ca7af0087b1b59485e46e1163b0c9326ef99323c2249d3c71c4917b972c5d621f1fa3b8b9686a89e78c744fe83379353d619e
  languageName: node
  linkType: hard

"alphanum-sort@npm:^1.0.2":
  version: 1.0.2
  resolution: "alphanum-sort@npm:1.0.2"
  checksum: 5a32d0b3c0944e65d22ff3ae2f88d7a4f8d88a78a703033caeae33f2944915e053d283d02f630dc94823edc7757148ecdcf39fd687a5117bda5c10133a03a7d8
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: ^4.1.0
  checksum: 6abfa08f2141d231c257162b15292467081fa49a208593e055c866aa0455b57f3a86b5a678c190c618faa79b4c59e254493099cb700dd9cf2293c6be2c8f5d8d
  languageName: node
  linkType: hard

"ansi-html-community@npm:^0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 04c568e8348a636963f915e48eaa3e01218322e1169acafdd79c384f22e5558c003f79bbc480c1563865497482817c7eed025f0653ebc17642fededa5cb42089
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.2
  resolution: "anymatch@npm:3.1.2"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 985163db2292fac9e5a1e072bf99f1b5baccf196e4de25a0b0b81865ebddeb3b3eb4480734ef0a2ac8c002845396b91aa89121f5b84f93981a4658164a9ec6e9
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^2.0.0":
  version: 2.0.0
  resolution: "are-we-there-yet@npm:2.0.0"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 6c80b4fd04ecee6ba6e737e0b72a4b41bdc64b7d279edfc998678567ff583c8df27e27523bc789f2c99be603ffa9eaa612803da1d886962d2086e7ff6fa90c7c
  languageName: node
  linkType: hard

"arg@npm:^5.0.0":
  version: 5.0.1
  resolution: "arg@npm:5.0.1"
  checksum: 9aefbcb1204f8dbd541a045bfe99b6515b4dc697c2f704ef2bb5e9fe5464575d97571e91e673a6f23ad72dd1cc24d7d8cf2d1d828e72c08e4d4f6f9237adc761
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.0":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"asap@npm:~2.0.3":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: b296c92c4b969e973260e47523207cd5769abd27c245a68c26dc7a0fe8053c55bb04360237cb51cab1df52be939da77150ace99ad331fb7fb13b3423ed73ff3d
  languageName: node
  linkType: hard

"async@npm:0.9.x":
  version: 0.9.2
  resolution: "async@npm:0.9.2"
  checksum: 87dbf129292b8a6c32a4e07f43f462498162aa86f404a7e11f978dbfdf75cfb163c26833684bb07b9d436083cd604cbbf730a57bfcbe436c6ae1ed266cdc56bb
  languageName: node
  linkType: hard

"async@npm:^2.6.2":
  version: 2.6.3
  resolution: "async@npm:2.6.3"
  dependencies:
    lodash: ^4.17.14
  checksum: 5e5561ff8fca807e88738533d620488ac03a5c43fce6c937451f7e35f943d33ad06c24af3f681a48cca3d2b0002b3118faff0a128dc89438a9bf0226f712c499
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"autocomplete.js@npm:^0.37.0":
  version: 0.37.1
  resolution: "autocomplete.js@npm:0.37.1"
  dependencies:
    immediate: ^3.2.3
  checksum: ef35f365ea1fcc100c0b90be9f8a142877b3175d93464804b2124d24d03c49aa81178dcc75ed0313f6c1c5f881e2812873f06b7c729854f3e3096a270d966e09
  languageName: node
  linkType: hard

"autocomplete.js@npm:^0.38.1":
  version: 0.38.1
  resolution: "autocomplete.js@npm:0.38.1"
  dependencies:
    immediate: ^3.2.3
  checksum: 167df1e55198ee7e526387b4ad5e0d4afc1db0066e77e1cdd016b8278915136e99a7f78b910445417df456615b810fc61b557a01f211245eaa7efd5cfffadf22
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.3.5, autoprefixer@npm:^10.3.7":
  version: 10.4.2
  resolution: "autoprefixer@npm:10.4.2"
  dependencies:
    browserslist: ^4.19.1
    caniuse-lite: ^1.0.30001297
    fraction.js: ^4.1.2
    normalize-range: ^0.1.2
    picocolors: ^1.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: dbd13e641eaa7d7e3121769c22cc439222f1a9d0371a583d12300849de7287ece1e793767ff9902842dbfd56c4b7c19ed9fe1947c9f343ba2f4f3519dbddfdef
  languageName: node
  linkType: hard

"axios@npm:^0.21.1":
  version: 0.21.4
  resolution: "axios@npm:0.21.4"
  dependencies:
    follow-redirects: ^1.14.0
  checksum: 44245f24ac971e7458f3120c92f9d66d1fc695e8b97019139de5b0cc65d9b8104647db01e5f46917728edfc0cfd88eb30fc4c55e6053eef4ace76768ce95ff3c
  languageName: node
  linkType: hard

"babel-loader@npm:^8.2.2":
  version: 8.2.3
  resolution: "babel-loader@npm:8.2.3"
  dependencies:
    find-cache-dir: ^3.3.1
    loader-utils: ^1.4.0
    make-dir: ^3.1.0
    schema-utils: ^2.6.5
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: 78e1e1a91954d644b6ce66366834d4d245febbc0fde33e4e2831725e83d6e760d12b3a78e9534ce92af69067bef1d9d9674df36d8c1f20ee127bc2354b2203ba
  languageName: node
  linkType: hard

"babel-plugin-apply-mdx-type-prop@npm:1.6.22":
  version: 1.6.22
  resolution: "babel-plugin-apply-mdx-type-prop@npm:1.6.22"
  dependencies:
    "@babel/helper-plugin-utils": 7.10.4
    "@mdx-js/util": 1.6.22
  peerDependencies:
    "@babel/core": ^7.11.6
  checksum: 43e2100164a8f3e46fddd76afcbfb1f02cbebd5612cfe63f3d344a740b0afbdc4d2bf5659cffe9323dd2554c7b86b23ebedae9dadcec353b6594f4292a1a28e2
  languageName: node
  linkType: hard

"babel-plugin-dynamic-import-node@npm:2.3.0":
  version: 2.3.0
  resolution: "babel-plugin-dynamic-import-node@npm:2.3.0"
  dependencies:
    object.assign: ^4.1.0
  checksum: 8a8a631bb5257f1ea7efc64533640aaabadea891c4ec1bcb4a6d10f88f76f326bce88013131a24ef1716ad1046e9919ddf06b6293a863af1178d45466452809d
  languageName: node
  linkType: hard

"babel-plugin-dynamic-import-node@npm:^2.3.3":
  version: 2.3.3
  resolution: "babel-plugin-dynamic-import-node@npm:2.3.3"
  dependencies:
    object.assign: ^4.1.0
  checksum: c9d24415bcc608d0db7d4c8540d8002ac2f94e2573d2eadced137a29d9eab7e25d2cbb4bc6b9db65cf6ee7430f7dd011d19c911a9a778f0533b4a05ce8292c9b
  languageName: node
  linkType: hard

"babel-plugin-extract-import-names@npm:1.6.22":
  version: 1.6.22
  resolution: "babel-plugin-extract-import-names@npm:1.6.22"
  dependencies:
    "@babel/helper-plugin-utils": 7.10.4
  checksum: 145ccf09c96d36411d340e78086555f8d4d5924ea39fcb0eca461c066cfa98bc4344982bb35eb85d054ef88f8d4dfc0205ba27370c1d8fcc78191b02908d044d
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.3.0":
  version: 0.3.0
  resolution: "babel-plugin-polyfill-corejs2@npm:0.3.0"
  dependencies:
    "@babel/compat-data": ^7.13.11
    "@babel/helper-define-polyfill-provider": ^0.3.0
    semver: ^6.1.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ffede597982066221291fe7c48ec1f1dda2b4ed3ee3e715436320697f35368223e1275bf095769d0b0c1115b90031dc525dd81b8ee9f6c8972cf1d2e10ad2b7d
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.5.0":
  version: 0.5.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.5.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.0
    core-js-compat: ^3.20.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cd3c9345d759a7a22e68fff3720aa70bd1ff5d47255206c767c1c831f7c68280f9769744088d2a93bcbe4391b708c21f099ff1eb6ceac7d50d2e0e334d8e97e4
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.3.0":
  version: 0.3.0
  resolution: "babel-plugin-polyfill-regenerator@npm:0.3.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ecca4389fd557554efc6de834f84f7c85e83c348d5283de2032d35429bc7121ed6f336553d3d704021f9bef22fca339fbee560d3b0fb8bb1d4eca2fecaaeebcb
  languageName: node
  linkType: hard

"babel-plugin-styled-components@npm:>= 1.12.0":
  version: 2.0.2
  resolution: "babel-plugin-styled-components@npm:2.0.2"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.0
    "@babel/helper-module-imports": ^7.16.0
    babel-plugin-syntax-jsx: ^6.18.0
    lodash: ^4.17.11
  peerDependencies:
    styled-components: ">= 2"
  checksum: 3de729e909506b8dda27567fa561aa2f50822f1151f0d321303790afa45ffc0698f0d6836b8c8fd5f24845f4e1b181f8c51532dd9c1fa7a15fa37033a445c137
  languageName: node
  linkType: hard

"babel-plugin-syntax-jsx@npm:^6.18.0":
  version: 6.18.0
  resolution: "babel-plugin-syntax-jsx@npm:6.18.0"
  checksum: 0c7ce5b81d6cfc01a7dd7a76a9a8f090ee02ba5c890310f51217ef1a7e6163fb7848994bbc14fd560117892e82240df9c7157ad0764da67ca5f2afafb73a7d27
  languageName: node
  linkType: hard

"bail@npm:^1.0.0":
  version: 1.0.5
  resolution: "bail@npm:1.0.5"
  checksum: 6c334940d7eaa4e656a12fb12407b6555649b6deb6df04270fa806e0da82684ebe4a4e47815b271c794b40f8d6fa286e0c248b14ddbabb324a917fab09b7301a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base16@npm:^1.0.0":
  version: 1.0.0
  resolution: "base16@npm:1.0.0"
  checksum: 0cd449a2db0f0f957e4b6b57e33bc43c9e20d4f1dd744065db94b5da35e8e71fa4dc4bc7a901e59a84d5f8b6936e3c520e2471787f667fc155fb0f50d8540f5d
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 61f9934c7378a51dce61b915586191078ef7f1c3eca707fdd58b96ff2ff56d9e0af2bdab66b1462301a73c73374239e6542d9821c0af787f3209a23365d07e7f
  languageName: node
  linkType: hard

"bcp-47-match@npm:^1.0.0":
  version: 1.0.3
  resolution: "bcp-47-match@npm:1.0.3"
  checksum: cde4cb72f640bc6925026e833109da490208ac81c3555a6b80b7daeeb438ecb285c0718fe433e982d8cf9355dc88efac4ce21a270baa130e1a3af6616fac1fd6
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"bluebird@npm:^3.7.1":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 869417503c722e7dc54ca46715f70e15f4d9c602a423a02c825570862d12935be59ed9c7ba34a9b31f186c017c23cac6b54e35446f8353059c101da73eac22ef
  languageName: node
  linkType: hard

"body-parser@npm:1.19.1":
  version: 1.19.1
  resolution: "body-parser@npm:1.19.1"
  dependencies:
    bytes: 3.1.1
    content-type: ~1.0.4
    debug: 2.6.9
    depd: ~1.1.2
    http-errors: 1.8.1
    iconv-lite: 0.4.24
    on-finished: ~2.3.0
    qs: 6.9.6
    raw-body: 2.4.2
    type-is: ~1.6.18
  checksum: 9197a300a6580b8723c7b6b1e22cebd5ba47cd4a6fd45c153350efcde79293869ddee8d17d95fb52724812d649d89d62775faab072608d3243a0cbb00582234e
  languageName: node
  linkType: hard

"bonjour@npm:^3.5.0":
  version: 3.5.0
  resolution: "bonjour@npm:3.5.0"
  dependencies:
    array-flatten: ^2.1.0
    deep-equal: ^1.0.1
    dns-equal: ^1.0.0
    dns-txt: ^2.0.2
    multicast-dns: ^6.0.1
    multicast-dns-service-types: ^1.1.0
  checksum: 2cfbe9fa861f4507b5ff3853eeae3ef03a231ede2b7363efedd80880ea3c0576f64416f98056c96e429ed68ff38dc4a70c0583d1eb4dab72e491ca44a0f03444
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0, boolbase@npm:~1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"boxen@npm:^5.0.0, boxen@npm:^5.0.1":
  version: 5.1.2
  resolution: "boxen@npm:5.1.2"
  dependencies:
    ansi-align: ^3.0.0
    camelcase: ^6.2.0
    chalk: ^4.1.0
    cli-boxes: ^2.2.1
    string-width: ^4.2.2
    type-fest: ^0.20.2
    widest-line: ^3.1.0
    wrap-ansi: ^7.0.0
  checksum: 82d03e42a72576ff235123f17b7c505372fe05c83f75f61e7d4fa4bcb393897ec95ce766fecb8f26b915f0f7a7227d66e5ec7cef43f5b2bd9d3aeed47ec55877
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"braces@npm:^3.0.1, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.14.5, browserslist@npm:^4.16.0, browserslist@npm:^4.16.5, browserslist@npm:^4.16.6, browserslist@npm:^4.17.5, browserslist@npm:^4.19.1":
  version: 4.19.1
  resolution: "browserslist@npm:4.19.1"
  dependencies:
    caniuse-lite: ^1.0.30001286
    electron-to-chromium: ^1.4.17
    escalade: ^3.1.1
    node-releases: ^2.0.1
    picocolors: ^1.0.0
  bin:
    browserslist: cli.js
  checksum: c0777fd483691638fd6801e16c9d809e1d65f6d2b06db2e806654be51045cbab1452a89841a2c5caea2cbe19d621b4f1d391cffbb24512aa33280039ab345875
  languageName: node
  linkType: hard

"buble-jsx-only@npm:^0.19.8":
  version: 0.19.8
  resolution: "buble-jsx-only@npm:0.19.8"
  dependencies:
    acorn: ^6.1.1
    acorn-dynamic-import: ^4.0.0
    acorn-jsx: ^5.0.1
    chalk: ^2.4.2
    magic-string: ^0.25.3
    minimist: ^1.2.0
    regexpu-core: ^4.5.4
  bin:
    buble: ./bin/buble
  checksum: 27f8b666065ec97f17cbbe5a599931154d99c4ad9bd6be59552ff83432f3f2649da90fa14cb7b690705c86fa38fca102c9031cc14b8c2bc968ac23785c8b8ed3
  languageName: node
  linkType: hard

"buble@npm:0.19.6":
  version: 0.19.6
  resolution: "buble@npm:0.19.6"
  dependencies:
    chalk: ^2.4.1
    magic-string: ^0.25.1
    minimist: ^1.2.0
    os-homedir: ^1.0.1
    regexpu-core: ^4.2.0
    vlq: ^1.0.0
  bin:
    buble: ./bin/buble
  checksum: 565fec70c3283de499f334804e1fade787ebd21bdc8cdb84b0466b852951bc9adca15f8d55cbc879f2361e898eae6adf4a4da0664e0913092228a1ae0ff7240e
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-indexof@npm:^1.0.0":
  version: 1.1.1
  resolution: "buffer-indexof@npm:1.1.1"
  checksum: 0967abc2981a8e7d776324c6b84811e4d84a7ead89b54a3bb8791437f0c4751afd060406b06db90a436f1cf771867331b5ecf5c4aca95b4ccb9f6cb146c22ebc
  languageName: node
  linkType: hard

"builtin-modules@npm:^3.1.0":
  version: 3.2.0
  resolution: "builtin-modules@npm:3.2.0"
  checksum: 0265aa1ba78e1a16f4e18668d815cb43fb364e6a6b8aa9189c6f44c7b894a551a43b323c40206959d2d4b2568c1f2805607ad6c88adc306a776ce6904cca6715
  languageName: node
  linkType: hard

"bytes@npm:3.0.0":
  version: 3.0.0
  resolution: "bytes@npm:3.0.0"
  checksum: a2b386dd8188849a5325f58eef69c3b73c51801c08ffc6963eddc9be244089ba32d19347caf6d145c86f315ae1b1fc7061a32b0c1aa6379e6a719090287ed101
  languageName: node
  linkType: hard

"bytes@npm:3.1.1":
  version: 3.1.1
  resolution: "bytes@npm:3.1.1"
  checksum: 949ab99a385d6acf4d2c69f1afc618615dc905936e0b0b9aa94a9e94d722baaba44d6a0851536585a0892ae4d462b5a270ccb1b04c774640742cbde5538ca328
  languageName: node
  linkType: hard

"cacache@npm:^15.2.0":
  version: 15.3.0
  resolution: "cacache@npm:15.3.0"
  dependencies:
    "@npmcli/fs": ^1.0.0
    "@npmcli/move-file": ^1.0.1
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    glob: ^7.1.4
    infer-owner: ^1.0.4
    lru-cache: ^6.0.0
    minipass: ^3.1.1
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.2
    mkdirp: ^1.0.3
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^8.0.1
    tar: ^6.0.2
    unique-filename: ^1.1.1
  checksum: a07327c27a4152c04eb0a831c63c00390d90f94d51bb80624a66f4e14a6b6360bbf02a84421267bd4d00ca73ac9773287d8d7169e8d2eafe378d2ce140579db8
  languageName: node
  linkType: hard

"cacheable-request@npm:^6.0.0":
  version: 6.1.0
  resolution: "cacheable-request@npm:6.1.0"
  dependencies:
    clone-response: ^1.0.2
    get-stream: ^5.1.0
    http-cache-semantics: ^4.0.0
    keyv: ^3.0.0
    lowercase-keys: ^2.0.0
    normalize-url: ^4.1.0
    responselike: ^1.0.2
  checksum: b510b237b18d17e89942e9ee2d2a077cb38db03f12167fd100932dfa8fc963424bfae0bfa1598df4ae16c944a5484e43e03df8f32105b04395ee9495e9e4e9f1
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: ^3.1.2
    tslib: ^2.0.3
  checksum: bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase-css@npm:2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 1cec2b3b3dcb5026688a470b00299a8db7d904c4802845c353dbd12d9d248d3346949a814d83bfd988d4d2e5b9904c07efe76fecd195a1d4f05b543e7c0b56b1
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.0
  resolution: "camelize@npm:1.0.0"
  checksum: 769f8d10071f57b974d9a51dc02f589dd7fb07ea6a7ecde1a57b52ae68657ba61fe85c60d50661b76c7dbb76b6474fbfd3356aee33cf5f025cd7fd6fb2811b73
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: ^4.0.0
    caniuse-lite: ^1.0.0
    lodash.memoize: ^4.1.2
    lodash.uniq: ^4.5.0
  checksum: db2a229383b20d0529b6b589dde99d7b6cb56ba371366f58cbbfa2929c9f42c01f873e2b6ef641d4eda9f0b4118de77dbb2805814670bdad4234bf08e720b0b4
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001286, caniuse-lite@npm:^1.0.30001297":
  version: 1.0.30001299
  resolution: "caniuse-lite@npm:1.0.30001299"
  checksum: c770f60ebf3e0cc8043ba4db0ebec12d7a595a6b50cb4437c3c5c55b04de9d2413f711f2828be761e8c37bb46b927a8abe6b199b8f0ffc1a34af0ebdee84be27
  languageName: node
  linkType: hard

"ccount@npm:^1.0.0, ccount@npm:^1.0.3":
  version: 1.1.0
  resolution: "ccount@npm:1.1.0"
  checksum: b335a79d0aa4308919cf7507babcfa04ac63d389ebed49dbf26990d4607c8a4713cde93cc83e707d84571ddfe1e7615dad248be9bc422ae4c188210f71b08b78
  languageName: node
  linkType: hard

"chalk@npm:^2.0.0, chalk@npm:^2.4.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-entities-legacy@npm:1.1.4"
  checksum: fe03a82c154414da3a0c8ab3188e4237ec68006cbcd681cf23c7cfb9502a0e76cd30ab69a2e50857ca10d984d57de3b307680fff5328ccd427f400e559c3a811
  languageName: node
  linkType: hard

"character-entities@npm:^1.0.0":
  version: 1.2.4
  resolution: "character-entities@npm:1.2.4"
  checksum: e1545716571ead57beac008433c1ff69517cd8ca5b336889321c5b8ff4a99c29b65589a701e9c086cda8a5e346a67295e2684f6c7ea96819fe85cbf49bf8686d
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-reference-invalid@npm:1.1.4"
  checksum: 20274574c70e05e2f81135f3b93285536bc8ff70f37f0809b0d17791a832838f1e49938382899ed4cb444e5bbd4314ca1415231344ba29f4222ce2ccf24fea0b
  languageName: node
  linkType: hard

"cheerio@npm:^0.22.0":
  version: 0.22.0
  resolution: "cheerio@npm:0.22.0"
  dependencies:
    css-select: ~1.2.0
    dom-serializer: ~0.1.0
    entities: ~1.1.1
    htmlparser2: ^3.9.1
    lodash.assignin: ^4.0.9
    lodash.bind: ^4.1.4
    lodash.defaults: ^4.0.1
    lodash.filter: ^4.4.0
    lodash.flatten: ^4.2.0
    lodash.foreach: ^4.3.0
    lodash.map: ^4.4.0
    lodash.merge: ^4.4.0
    lodash.pick: ^4.2.1
    lodash.reduce: ^4.4.0
    lodash.reject: ^4.4.0
    lodash.some: ^4.4.0
  checksum: b0a6cfa61eb7ae96e4cb8cfeeb14eb45bb790fa40098509268629c4cecca5b99124aabe6daa1154c497ac8def47bc3f9706cef5f0e8a6177a0c137d4bdaaf8b7
  languageName: node
  linkType: hard

"chokidar@npm:^3.4.2, chokidar@npm:^3.5.2":
  version: 3.5.2
  resolution: "chokidar@npm:3.5.2"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d1fda32fcd67d9f6170a8468ad2630a3c6194949c9db3f6a91b16478c328b2800f433fb5d2592511b6cb145a47c013ea1cce60b432b1a001ae3ee978a8bffc2d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"classnames@npm:^2.2.6, classnames@npm:^2.3.1":
  version: 2.3.1
  resolution: "classnames@npm:2.3.1"
  checksum: 14db8889d56c267a591f08b0834989fe542d47fac659af5a539e110cc4266694e8de86e4e3bbd271157dbd831361310a8293e0167141e80b0f03a0f175c80960
  languageName: node
  linkType: hard

"clean-css@npm:^5.1.5, clean-css@npm:^5.2.2":
  version: 5.2.2
  resolution: "clean-css@npm:5.2.2"
  dependencies:
    source-map: ~0.6.0
  checksum: 10855820829b8b6ea94e462313fdc177b297aca5c7870a969591549d6a766824f912b5e58773bd345b2a7effae863ab492258b5a77a40029fba6d11d861cbee3
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-boxes@npm:^2.2.1":
  version: 2.2.1
  resolution: "cli-boxes@npm:2.2.1"
  checksum: be79f8ec23a558b49e01311b39a1ea01243ecee30539c880cf14bf518a12e223ef40c57ead0cb44f509bffdffc5c129c746cd50d863ab879385370112af4f585
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.2
  resolution: "clone-response@npm:1.0.2"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 2d0e61547fc66276e0903be9654ada422515f5a15741691352000d47e8c00c226061221074ce2c0064d12e975e84a8687cfd35d8b405750cb4e772f87b256eda
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.1.1
  resolution: "clsx@npm:1.1.1"
  checksum: ff052650329773b9b245177305fc4c4dc3129f7b2be84af4f58dc5defa99538c61d4207be7419405a5f8f3d92007c954f4daba5a7b74e563d5de71c28c830063
  languageName: node
  linkType: hard

"collapse-white-space@npm:^1.0.2":
  version: 1.0.6
  resolution: "collapse-white-space@npm:1.0.6"
  checksum: 9673fb797952c5c888341435596c69388b22cd5560c8cd3f40edb72734a9c820f56a7c9525166bcb7068b5d5805372e6fd0c4b9f2869782ad070cb5d3faf26e7
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.2":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"colord@npm:^2.9.1":
  version: 2.9.2
  resolution: "colord@npm:2.9.2"
  checksum: 2aa6a9b3abbce74ba3c563886cfeb433ea0d7df5ad6f4a560005eddab1ddf7c0fc98f39b09b599767a19c86dd3837b77f66f036e479515d4b17347006dbd6d9f
  languageName: node
  linkType: hard

"colorette@npm:^2.0.10":
  version: 2.0.16
  resolution: "colorette@npm:2.0.16"
  checksum: cd55596a3a2d1071c1a28eee7fd8a5387593ff1bd10a3e8d0a6221499311fe34a9f2b9272d77c391e0e003dcdc8934fb2f8d106e7ef1f7516f8060c901d41a27
  languageName: node
  linkType: hard

"combine-promises@npm:^1.1.0":
  version: 1.1.0
  resolution: "combine-promises@npm:1.1.0"
  checksum: 23b55f66d5cea3ddf39608c07f7a96065c7bb7cc4f54c7f217040771262ad97e808b30f7f267c553a9ca95552fc9813fb465232f5d82e190e118b33238186af8
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^1.0.0":
  version: 1.0.8
  resolution: "comma-separated-tokens@npm:1.0.8"
  checksum: 0adcb07174fa4d08cf0f5c8e3aec40a36b5ff0c2c720e5e23f50fe02e6789d1d00a67036c80e0c1e1539f41d3e7f0101b074039dd833b4e4a59031b659d6ca0d
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^5.1.0":
  version: 5.1.0
  resolution: "commander@npm:5.1.0"
  checksum: 0b7fec1712fbcc6230fcb161d8d73b4730fa91a21dc089515489402ad78810547683f058e2a9835929c212fead1d6a6ade70db28bbb03edbc2829a9ab7d69447
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"common-tags@npm:^1.8.0":
  version: 1.8.2
  resolution: "common-tags@npm:1.8.2"
  checksum: 767a6255a84bbc47df49a60ab583053bb29a7d9687066a18500a516188a062c4e4cd52de341f22de0b07062e699b1b8fe3cfa1cb55b241cb9301aeb4f45b4dff
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"component-props@npm:1.1.1":
  version: 1.1.1
  resolution: "component-props@npm:1.1.1"
  checksum: fda496d2cbe0a1de223741cafce1c3dd35214d6ad86b6b1a1c57109d047e0de149d8bbbb9fe4c39e90ad5902f0026da524a6355683c23dc12c552c3d0ddf8dcf
  languageName: node
  linkType: hard

"component-xor@npm:0.0.4":
  version: 0.0.4
  resolution: "component-xor@npm:0.0.4"
  checksum: 3ef1bfadbe99a6c1d557fa4b22c49158987dc30177d08d0926f0663185cb154438c6e76e86cf7e30b7f2ee4c4af9e41f9da19b56ba391ab3c773aaa2bf5c1f2a
  languageName: node
  linkType: hard

"compressible@npm:~2.0.16":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.7.4
  resolution: "compression@npm:1.7.4"
  dependencies:
    accepts: ~1.3.5
    bytes: 3.0.0
    compressible: ~2.0.16
    debug: 2.6.9
    on-headers: ~1.0.2
    safe-buffer: 5.1.2
    vary: ~1.1.2
  checksum: 35c0f2eb1f28418978615dc1bc02075b34b1568f7f56c62d60f4214d4b7cc00d0f6d282b5f8a954f59872396bd770b6b15ffd8aa94c67d4bce9b8887b906999b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"configstore@npm:^5.0.1":
  version: 5.0.1
  resolution: "configstore@npm:5.0.1"
  dependencies:
    dot-prop: ^5.2.0
    graceful-fs: ^4.1.2
    make-dir: ^3.0.0
    unique-string: ^2.0.0
    write-file-atomic: ^3.0.0
    xdg-basedir: ^4.0.0
  checksum: 60ef65d493b63f96e14b11ba7ec072fdbf3d40110a94fb7199d1c287761bdea5c5244e76b2596325f30c1b652213aa75de96ea20afd4a5f82065e61ea090988e
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^1.6.0":
  version: 1.6.0
  resolution: "connect-history-api-fallback@npm:1.6.0"
  checksum: 804ca2be28c999032ecd37a9f71405e5d7b7a4b3defcebbe41077bb8c5a0a150d7b59f51dcc33b2de30bc7e217a31d10f8cfad27e8e74c2fc7655eeba82d6e7e
  languageName: node
  linkType: hard

"consola@npm:^2.15.3":
  version: 2.15.3
  resolution: "consola@npm:2.15.3"
  checksum: 8ef7a09b703ec67ac5c389a372a33b6dc97eda6c9876443a60d76a3076eea0259e7f67a4e54fd5a52f97df73690822d090cf8b7e102b5761348afef7c6d03e28
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.0.0, console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.2":
  version: 0.5.2
  resolution: "content-disposition@npm:0.5.2"
  checksum: 298d7da63255a38f7858ee19c7b6aae32b167e911293174b4c1349955e97e78e1d0b0d06c10e229405987275b417cf36ff65cbd4821a98bc9df4e41e9372cde7
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4":
  version: 1.0.4
  resolution: "content-type@npm:1.0.4"
  checksum: 3d93585fda985d1554eca5ebd251994327608d2e200978fdbfba21c0c679914d5faf266d17027de44b34a72c7b0745b18584ecccaa7e1fdfb6a68ac7114f12e0
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.7.0":
  version: 1.8.0
  resolution: "convert-source-map@npm:1.8.0"
  dependencies:
    safe-buffer: ~5.1.1
  checksum: 985d974a2d33e1a2543ada51c93e1ba2f73eaed608dc39f229afc78f71dcc4c8b7d7c684aa647e3c6a3a204027444d69e53e169ce94e8d1fa8d7dee80c9c8fed
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.4.1":
  version: 0.4.1
  resolution: "cookie@npm:0.4.1"
  checksum: bd7c47f5d94ab70ccdfe8210cde7d725880d2fcda06d8e375afbdd82de0c8d3b73541996e9ce57d35f67f672c4ee6d60208adec06b3c5fc94cebb85196084cf8
  languageName: node
  linkType: hard

"copy-text-to-clipboard@npm:^3.0.1":
  version: 3.0.1
  resolution: "copy-text-to-clipboard@npm:3.0.1"
  checksum: 4c301b9a65c8bf337e26a74d28849096651697fac829a364c463df81ba5ddfeea0741214f9f1232832fffd229ebd5659d3abcccea3fe54d7010a22e515cc38bc
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:^9.0.1":
  version: 9.1.0
  resolution: "copy-webpack-plugin@npm:9.1.0"
  dependencies:
    fast-glob: ^3.2.7
    glob-parent: ^6.0.1
    globby: ^11.0.3
    normalize-path: ^3.0.0
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.0
  peerDependencies:
    webpack: ^5.1.0
  checksum: 06cb4fb6fc99a95ccfd3169115ee57f64953e5b4075900fc8faab98b7e7d3fcd6915b125fdb98c919cfd55e581a8444f5c6b9dbb342cbd60b154d8fb3f79f2b9
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.20.0, core-js-compat@npm:^3.20.2":
  version: 3.20.2
  resolution: "core-js-compat@npm:3.20.2"
  dependencies:
    browserslist: ^4.19.1
    semver: 7.0.0
  checksum: 303fcf5dede7363d484ebacdbdb6924e8c8493168b5db0536f8afe3c0fadc54333616d311e70146ab4e35b7c7bc8982a68af18a58085a767c9162c3242ba3451
  languageName: node
  linkType: hard

"core-js-pure@npm:^3.20.2":
  version: 3.20.2
  resolution: "core-js-pure@npm:3.20.2"
  checksum: d6b3f6782e3f2fc27eb2335917d5c5d0e7621e424c25da67429e9b48b7708b76fdc4a178b245421eeb8342c0ea9b0ca636ece002db3d0e68246a9d395d461ca7
  languageName: node
  linkType: hard

"core-js@npm:^2.4.1":
  version: 2.6.12
  resolution: "core-js@npm:2.6.12"
  checksum: 44fa9934a85f8c78d61e0c8b7b22436330471ffe59ec5076fe7f324d6e8cf7f824b14b1c81ca73608b13bdb0fef035bd820989bf059767ad6fa13123bb8bd016
  languageName: node
  linkType: hard

"core-js@npm:^3.18.0":
  version: 3.20.2
  resolution: "core-js@npm:3.20.2"
  checksum: dc055a5a79f2e3c2e060b4fb8a3e228fa0de16464bff3c6605646183139eebaa68c390ec6d9c3acc5677f333df510b977ee3f1d5992e6c36b1f200e35f5ed8ca
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^6.0.0":
  version: 6.0.0
  resolution: "cosmiconfig@npm:6.0.0"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.1.0
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.7.2
  checksum: 8eed7c854b91643ecb820767d0deb038b50780ecc3d53b0b19e03ed8aabed4ae77271198d1ae3d49c3b110867edf679f5faad924820a8d1774144a87cb6f98fc
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0, cosmiconfig@npm:^7.0.1":
  version: 7.0.1
  resolution: "cosmiconfig@npm:7.0.1"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: 4be63e7117955fd88333d7460e4c466a90f556df6ef34efd59034d2463484e339666c41f02b523d574a797ec61f4a91918c5b89a316db2ea2f834e0d2d09465b
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.0.4":
  version: 3.1.4
  resolution: "cross-fetch@npm:3.1.4"
  dependencies:
    node-fetch: 2.6.1
  checksum: 2107e5e633aa327bdacab036b1907c7ddd28651ede0c1d4fd14db04510944d56849a8255e2f5b8f9a1da0e061b6cee943f6819fe29ed9a130195e7fadd82a4ff
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 0283879f55e7c16fdceacc181f87a0a65c53bc16ffe1d58b9d19a6277adcd71900d02bb2c4843dd55e78c51e30e89b0fec618a7f170ebcc95b33182c28f05fd6
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 8f125e3ad477bd03c77b533044bd9e8a6f7c0da52d49bbc0bbe38327b3829d6ba04d368ca49dd9ff3b667d2fc8f1698d891c198bbf8feade1a5501bf5a296408
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^6.0.3":
  version: 6.1.4
  resolution: "css-declaration-sorter@npm:6.1.4"
  dependencies:
    timsort: ^0.3.0
  peerDependencies:
    postcss: ^8.0.9
  checksum: 72800a234f0d4facf44a5b504170749b854cd3bd6bf16d0955b3e70a1b613cec0192f585a81e8db1f03c035b13ca9494698a7eaaf861150db51c2f8f643e8ffb
  languageName: node
  linkType: hard

"css-loader@npm:^5.1.1":
  version: 5.2.7
  resolution: "css-loader@npm:5.2.7"
  dependencies:
    icss-utils: ^5.1.0
    loader-utils: ^2.0.0
    postcss: ^8.2.15
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.0
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.1.0
    schema-utils: ^3.0.0
    semver: ^7.3.5
  peerDependencies:
    webpack: ^4.27.0 || ^5.0.0
  checksum: fb0742b30ac0919f94b99a323bdefe6d48ae46d66c7d966aae59031350532f368f8bba5951fcd268f2e053c5e6e4655551076268e9073ccb58e453f98ae58f8e
  languageName: node
  linkType: hard

"css-minimizer-webpack-plugin@npm:^3.0.2":
  version: 3.3.1
  resolution: "css-minimizer-webpack-plugin@npm:3.3.1"
  dependencies:
    cssnano: ^5.0.6
    jest-worker: ^27.0.2
    postcss: ^8.3.5
    schema-utils: ^4.0.0
    serialize-javascript: ^6.0.0
    source-map: ^0.6.1
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    clean-css:
      optional: true
    csso:
      optional: true
    esbuild:
      optional: true
  checksum: 5e054ef608702dba55dec629503618acfbebb9566f92058a1ebf372f0feb47b9faa3de7142372be629fa327a7223c614b325a08d955d080a64a06983c8782321
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.2.1
  resolution: "css-select@npm:4.2.1"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^5.1.0
    domhandler: ^4.3.0
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: 6617193ec7c332217204c4ea371d332c6845603fda415e36032e7e9e18206d7c368a14e3c57532082314d2689955b01122aa1097c1c52b6c1cab7ad90970d3c6
  languageName: node
  linkType: hard

"css-select@npm:~1.2.0":
  version: 1.2.0
  resolution: "css-select@npm:1.2.0"
  dependencies:
    boolbase: ~1.0.0
    css-what: 2.1
    domutils: 1.5.1
    nth-check: ~1.0.1
  checksum: 607cca60d2f5c56701fe5f800bbe668b114395c503d4e4808edbbbe70b8be3c96a6407428dc0227fcbdf335b20468e6a9e7fd689185edfb57d402e1e4837c9b7
  languageName: node
  linkType: hard

"css-selector-parser@npm:^1.0.0":
  version: 1.4.1
  resolution: "css-selector-parser@npm:1.4.1"
  checksum: 31948754e579eedb918c2fb2d5a4c643ec769ff4a0d03a7bd10b43b25d44973f8cbe86d7ec00c4494269f7ff38b3d2ab0f6ea801cece0ef0974e74469dff770c
  languageName: node
  linkType: hard

"css-to-react-native@npm:^3.0.0":
  version: 3.0.0
  resolution: "css-to-react-native@npm:3.0.0"
  dependencies:
    camelize: ^1.0.0
    css-color-keywords: ^1.0.0
    postcss-value-parser: ^4.0.2
  checksum: 98a2e9d4fbe9cabc8b744dfdd5ec108396ce497a7b860912a95b299bd52517461281810fcb707965a021a8be39adca9587184a26fb4e926211391a1557aca3c1
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2, css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-what@npm:2.1":
  version: 2.1.3
  resolution: "css-what@npm:2.1.3"
  checksum: a52d56c591a7e1c37506d0d8c4fdef72537fb8eb4cb68711485997a88d76b5a3342b73a7c79176268f95b428596c447ad7fa3488224a6b8b532e2f1f2ee8545c
  languageName: node
  linkType: hard

"css-what@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-what@npm:5.1.0"
  checksum: 0b75d1bac95c885c168573c85744a6c6843d8c33345f54f717218b37ea6296b0e99bb12105930ea170fd4a921990392a7c790c16c585c1d8960c49e2b7ec39f7
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssnano-preset-advanced@npm:^5.1.4":
  version: 5.1.10
  resolution: "cssnano-preset-advanced@npm:5.1.10"
  dependencies:
    autoprefixer: ^10.3.7
    cssnano-preset-default: ^5.1.10
    postcss-discard-unused: ^5.0.1
    postcss-merge-idents: ^5.0.2
    postcss-reduce-idents: ^5.0.1
    postcss-zindex: ^5.0.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: 4d9c0c5a8c03716a5221c9472ebd4e076735d4ed6db573894c7cf33a19ebf7356085368a76cf05d7cd4ee73e32c69a534347526029fb76e7ecab8ca6a589f4bb
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^5.1.10":
  version: 5.1.10
  resolution: "cssnano-preset-default@npm:5.1.10"
  dependencies:
    css-declaration-sorter: ^6.0.3
    cssnano-utils: ^3.0.0
    postcss-calc: ^8.2.0
    postcss-colormin: ^5.2.3
    postcss-convert-values: ^5.0.2
    postcss-discard-comments: ^5.0.1
    postcss-discard-duplicates: ^5.0.1
    postcss-discard-empty: ^5.0.1
    postcss-discard-overridden: ^5.0.2
    postcss-merge-longhand: ^5.0.4
    postcss-merge-rules: ^5.0.4
    postcss-minify-font-values: ^5.0.2
    postcss-minify-gradients: ^5.0.4
    postcss-minify-params: ^5.0.3
    postcss-minify-selectors: ^5.1.1
    postcss-normalize-charset: ^5.0.1
    postcss-normalize-display-values: ^5.0.2
    postcss-normalize-positions: ^5.0.2
    postcss-normalize-repeat-style: ^5.0.2
    postcss-normalize-string: ^5.0.2
    postcss-normalize-timing-functions: ^5.0.2
    postcss-normalize-unicode: ^5.0.2
    postcss-normalize-url: ^5.0.4
    postcss-normalize-whitespace: ^5.0.2
    postcss-ordered-values: ^5.0.3
    postcss-reduce-initial: ^5.0.2
    postcss-reduce-transforms: ^5.0.2
    postcss-svgo: ^5.0.3
    postcss-unique-selectors: ^5.0.2
  peerDependencies:
    postcss: ^8.2.15
  checksum: c5170319bdd915faf36e71ace4a6e7c755258b4084fd3e6fcc46c2fd3256bb583fe582c36a3c06b2b78bc7075539ae1da7bdc3051e6b94189326f31c85ca503e
  languageName: node
  linkType: hard

"cssnano-utils@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssnano-utils@npm:3.0.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 7a730c8e5411cf7dd2bb516fd7158488263a9ea6a50b7b2d284cd46714a9a880d3a415d3026760830a5b11adb818f3bde9c1ded907b9daf5cc5cd9d9e8a33e7e
  languageName: node
  linkType: hard

"cssnano@npm:^5.0.6, cssnano@npm:^5.0.8":
  version: 5.0.15
  resolution: "cssnano@npm:5.0.15"
  dependencies:
    cssnano-preset-default: ^5.1.10
    lilconfig: ^2.0.3
    yaml: ^1.10.2
  peerDependencies:
    postcss: ^8.2.15
  checksum: 7b4d5d8a00b28a7dceab4d8f23d3d8e71ec8c26d445b828f70ca151bda1fe5ff033279d28064620c3f2b7a0b015f99c7887ccae505cf8c87219dcbe4bf7de30b
  languageName: node
  linkType: hard

"csso@npm:^4.2.0":
  version: 4.2.0
  resolution: "csso@npm:4.2.0"
  dependencies:
    css-tree: ^1.1.2
  checksum: 380ba9663da3bcea58dee358a0d8c4468bb6539be3c439dc266ac41c047217f52fd698fb7e4b6b6ccdfb8cf53ef4ceed8cc8ceccb8dfca2aa628319826b5b998
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.0.10
  resolution: "csstype@npm:3.0.10"
  checksum: 20a8fa324f2b33ddf94aa7507d1b6ab3daa6f3cc308888dc50126585d7952f2471de69b2dbe0635d1fdc31223fef8e070842691877e725caf456e2378685a631
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.6.0":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1":
  version: 4.3.3
  resolution: "debug@npm:4.3.3"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 14472d56fe4a94dbcfaa6dbed2dd3849f1d72ba78104a1a328047bb564643ca49df0224c3a17fa63533fd11dd3d4c8636cd861191232a2c6735af00cc2d4de16
  languageName: node
  linkType: hard

"debug@npm:^3.1.1":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decompress-response@npm:^3.3.0":
  version: 3.3.0
  resolution: "decompress-response@npm:3.3.0"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 952552ac3bd7de2fc18015086b09468645c9638d98a551305e485230ada278c039c91116e946d07894b39ee53c0f0d5b6473f25a224029344354513b412d7380
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.1
  resolution: "deep-equal@npm:1.1.1"
  dependencies:
    is-arguments: ^1.0.4
    is-date-object: ^1.0.1
    is-regex: ^1.0.4
    object-is: ^1.0.1
    object-keys: ^1.1.1
    regexp.prototype.flags: ^1.2.0
  checksum: f92686f2c5bcdf714a75a5fa7a9e47cb374a8ec9307e717b8d1ce61f56a75aaebf5619c2a12b8087a705b5a2f60d0292c35f8b58cb1f72e3268a3a15cab9f78d
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.2.2
  resolution: "deepmerge@npm:4.2.2"
  checksum: a8c43a1ed8d6d1ed2b5bf569fa4c8eb9f0924034baf75d5d406e47e157a451075c4db353efea7b6bcc56ec48116a8ce72fccf867b6e078e7c561904b5897530b
  languageName: node
  linkType: hard

"default-gateway@npm:^6.0.3":
  version: 6.0.3
  resolution: "default-gateway@npm:6.0.3"
  dependencies:
    execa: ^5.0.0
  checksum: 126f8273ecac8ee9ff91ea778e8784f6cd732d77c3157e8c5bdd6ed03651b5291f71446d05bc02d04073b1e67583604db5394ea3cf992ede0088c70ea15b7378
  languageName: node
  linkType: hard

"defer-to-connect@npm:^1.0.1":
  version: 1.1.3
  resolution: "defer-to-connect@npm:1.1.3"
  checksum: 9491b301dcfa04956f989481ba7a43c2231044206269eb4ab64a52d6639ee15b1252262a789eb4239fb46ab63e44d4e408641bae8e0793d640aee55398cb3930
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3":
  version: 1.1.3
  resolution: "define-properties@npm:1.1.3"
  dependencies:
    object-keys: ^1.0.12
  checksum: da80dba55d0cd76a5a7ab71ef6ea0ebcb7b941f803793e4e0257b384cb772038faa0c31659d244e82c4342edef841c1a1212580006a05a5068ee48223d787317
  languageName: node
  linkType: hard

"del@npm:^6.0.0":
  version: 6.0.0
  resolution: "del@npm:6.0.0"
  dependencies:
    globby: ^11.0.1
    graceful-fs: ^4.2.4
    is-glob: ^4.0.1
    is-path-cwd: ^2.2.0
    is-path-inside: ^3.0.2
    p-map: ^4.0.0
    rimraf: ^3.0.2
    slash: ^3.0.0
  checksum: 5742891627e91aaf62385714025233f4664da28bc55b6ab825649dcdea4691fed3cf329a2b1913fd2d2612e693e99e08a03c84cac7f36ef54bacac9390520192
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:^1.1.2, depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"destroy@npm:~1.0.4":
  version: 1.0.4
  resolution: "destroy@npm:1.0.4"
  checksum: da9ab4961dc61677c709da0c25ef01733042614453924d65636a7db37308fef8a24cd1e07172e61173d471ca175371295fbc984b0af5b2b4ff47cd57bd784c03
  languageName: node
  linkType: hard

"detab@npm:2.0.4":
  version: 2.0.4
  resolution: "detab@npm:2.0.4"
  dependencies:
    repeat-string: ^1.5.4
  checksum: 34b077521ecd4c6357d32ff7923be644d34aa6f6b7d717d40ec4a9168243eefaea2b512a75a460a6f70c31b0bbc31ff90f820a891803b4ddaf99e9d04d0d389d
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"detect-port-alt@npm:^1.1.6":
  version: 1.1.6
  resolution: "detect-port-alt@npm:1.1.6"
  dependencies:
    address: ^1.0.1
    debug: ^2.6.0
  bin:
    detect: ./bin/detect-port
    detect-port: ./bin/detect-port
  checksum: 9dc37b1fa4a9dd6d4889e1045849b8d841232b598d1ca888bf712f4035b07a17cf6d537465a0d7323250048d3a5a0540e3b7cf89457efc222f96f77e2c40d16a
  languageName: node
  linkType: hard

"detect-port@npm:^1.3.0":
  version: 1.3.0
  resolution: "detect-port@npm:1.3.0"
  dependencies:
    address: ^1.0.1
    debug: ^2.6.0
  bin:
    detect: ./bin/detect-port
    detect-port: ./bin/detect-port
  checksum: 93c40febe714f56711d1fedc2b7a9cc4cbaa0fcddec0509876c46b9dd6099ed6bfd6662a4f35e5fa0301660f48ed516829253ab0fc90b9e79b823dd77786b379
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"direction@npm:^1.0.0":
  version: 1.0.4
  resolution: "direction@npm:1.0.4"
  bin:
    direction: cli.js
  checksum: 572ac399093d7c9f2181c96828d252922e2a962b8f31a7fc118e3f7619592c566cc2ed313baf7703f17b2be00cd3c1402550140d0c3f4f70362976376a08b095
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: a8471ac849c7c13824f053babea1bc26e2f359394dd5a460f8340d8abd13434be01e3327a5c59d212f8c8997817450efd3f3ac77bec709b21979cf0235644524
  languageName: node
  linkType: hard

"dns-packet@npm:^1.3.1":
  version: 1.3.4
  resolution: "dns-packet@npm:1.3.4"
  dependencies:
    ip: ^1.1.0
    safe-buffer: ^5.0.1
  checksum: 7dd87f85cb4f9d1a99c03470730e3d9385e67dc94f6c13868c4034424a5378631e492f9f1fbc43d3c42f319fbbfe18b6488bb9527c32d34692c52bf1f5eedf69
  languageName: node
  linkType: hard

"dns-txt@npm:^2.0.2":
  version: 2.0.2
  resolution: "dns-txt@npm:2.0.2"
  dependencies:
    buffer-indexof: ^1.0.0
  checksum: 80130b665379ecd991687ae079fbee25d091e03e4c4cef41e7643b977849ac48c2f56bfcb3727e53594d29029b833749811110d9f3fbee1b26a6e6f8096a5cef
  languageName: node
  linkType: hard

"docusaurus-lunr-search@npm:^2.1.15":
  version: 2.1.15
  resolution: "docusaurus-lunr-search@npm:2.1.15"
  dependencies:
    autocomplete.js: ^0.37.0
    classnames: ^2.2.6
    gauge: ^3.0.0
    hast-util-select: ^4.0.0
    hast-util-to-text: ^2.0.0
    hogan.js: ^3.0.2
    lunr: ^2.3.8
    lunr-languages: ^1.4.0
    minimatch: ^3.0.4
    object-assign: ^4.1.1
    rehype-parse: ^7.0.1
    to-vfile: ^6.1.0
    unified: ^9.0.0
    unist-util-is: ^4.0.2
  peerDependencies:
    "@docusaurus/core": ^2.0.0-alpha.60 || ^2.0.0
    react: ^16.8.4 || ^17
    react-dom: ^16.8.4 || ^17
  checksum: 3e1019125f57be8a397ad54e88a8163f846d46d9f2282987f41c9c23c2ae94bef037cd25081266281f36adec3d5807c1caeb04140096a96d6896db6d580bcbf6
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: ~0.4
  checksum: ea52fe303f5392e48dea563abef0e6fb3a478b8dbe3c599e99bb5d53981c6c38fc4944e56bb92a8ead6bb989d10b7914722ae11febbd2fd0910e33b9fc4aaa77
  languageName: node
  linkType: hard

"dom-iterator@npm:^1.0.0":
  version: 1.0.0
  resolution: "dom-iterator@npm:1.0.0"
  dependencies:
    component-props: 1.1.1
    component-xor: 0.0.4
  checksum: ad02e194985969fbf3c2a73897b667f81c15f28e4e6090d853f2062737b619825dc153744cd3fa7bf71a43305041e72fe2df99bdb80480166593358438a7de75
  languageName: node
  linkType: hard

"dom-serializer@npm:0":
  version: 0.2.2
  resolution: "dom-serializer@npm:0.2.2"
  dependencies:
    domelementtype: ^2.0.1
    entities: ^2.0.0
  checksum: 376344893e4feccab649a14ca1a46473e9961f40fe62479ea692d4fee4d9df1c00ca8654811a79c1ca7b020096987e1ca4fb4d7f8bae32c1db800a680a0e5d5e
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.3.2
  resolution: "dom-serializer@npm:1.3.2"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: bff48714944d67b160db71ba244fb0f3fe72e77ef2ec8414e2eeb56f2d926e404a13456b8b83a5392e217ba47dec2ec0c368801b31481813e94d185276c3e964
  languageName: node
  linkType: hard

"dom-serializer@npm:~0.1.0":
  version: 0.1.1
  resolution: "dom-serializer@npm:0.1.1"
  dependencies:
    domelementtype: ^1.3.0
    entities: ^1.1.1
  checksum: 4f6a3eff802273741931cfd3c800fab4e683236eed10628d6605f52538a6bc0ce4770f3ca2ad68a27412c103ae9b6cdaed3c0a8e20d2704192bde497bc875215
  languageName: node
  linkType: hard

"domelementtype@npm:1, domelementtype@npm:^1.3.0, domelementtype@npm:^1.3.1":
  version: 1.3.1
  resolution: "domelementtype@npm:1.3.1"
  checksum: 7893da40218ae2106ec6ffc146b17f203487a52f5228b032ea7aa470e41dfe03e1bd762d0ee0139e792195efda765434b04b43cddcf63207b098f6ae44b36ad6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.2.0
  resolution: "domelementtype@npm:2.2.0"
  checksum: 24cb386198640cd58aa36f8c987f2ea61859929106d06ffcc8f547e70cb2ed82a6dc56dcb8252b21fba1f1ea07df6e4356d60bfe57f77114ca1aed6828362629
  languageName: node
  linkType: hard

"domhandler@npm:^2.3.0":
  version: 2.4.2
  resolution: "domhandler@npm:2.4.2"
  dependencies:
    domelementtype: 1
  checksum: 49bd70c9c784f845cd047e1dfb3611bd10891c05719acfc93f01fc726a419ed09fbe0b69f9064392d556a63fffc5a02010856cedae9368f4817146d95a97011f
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.0":
  version: 4.3.0
  resolution: "domhandler@npm:4.3.0"
  dependencies:
    domelementtype: ^2.2.0
  checksum: d2a2dbf40dd99abf936b65ad83c6b530afdb3605a87cad37a11b5d9220e68423ebef1b86c89e0f6d93ffaf315cc327cf1a988652e7a9a95cce539e3984f4c64d
  languageName: node
  linkType: hard

"domutils@npm:1.5.1":
  version: 1.5.1
  resolution: "domutils@npm:1.5.1"
  dependencies:
    dom-serializer: 0
    domelementtype: 1
  checksum: 800d1f9d1c2e637267dae078ff6e24461e6be1baeb52fa70f2e7e7520816c032a925997cd15d822de53ef9896abb1f35e5c439d301500a9cd6b46a395f6f6ca0
  languageName: node
  linkType: hard

"domutils@npm:^1.5.1":
  version: 1.7.0
  resolution: "domutils@npm:1.7.0"
  dependencies:
    dom-serializer: 0
    domelementtype: 1
  checksum: f60a725b1f73c1ae82f4894b691601ecc6ecb68320d87923ac3633137627c7865725af813ae5d188ad3954283853bcf46779eb50304ec5d5354044569fcefd2b
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dot-prop@npm:^5.2.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: d5775790093c234ef4bfd5fbe40884ff7e6c87573e5339432870616331189f7f5d86575c5b5af2dcf0f61172990f4f734d07844b1f23482fff09e3c4bead05ea
  languageName: node
  linkType: hard

"duplexer3@npm:^0.1.4":
  version: 0.1.4
  resolution: "duplexer3@npm:0.1.4"
  checksum: c2fd6969314607d23439c583699aaa43c4100d66b3e161df55dccd731acc57d5c81a64bb4f250805fbe434ddb1d2623fee2386fb890f5886ca1298690ec53415
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.1, duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"ejs@npm:^3.1.6":
  version: 3.1.6
  resolution: "ejs@npm:3.1.6"
  dependencies:
    jake: ^10.6.1
  bin:
    ejs: ./bin/cli.js
  checksum: 81a9cdea0b4ded3b5a4b212b7c17e20bb07468f08394e2d519708d367957a70aef3d282a6d5d38bf6ad313ba25802b9193d4227f29b084d2ee0f28d115141d48
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.17":
  version: 1.4.44
  resolution: "electron-to-chromium@npm:1.4.44"
  checksum: 76b0c723cf4fa3f542e4e34add8c559d5c3668192d1f359d24b45546b34791dff716e9fd905fbcddd6875cb849b72106c6f5edd682bba2f78c1c2eb6bccd647d
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"emoticon@npm:^3.2.0":
  version: 3.2.0
  resolution: "emoticon@npm:3.2.0"
  checksum: f30649d18b672ab3139e95cb04f77b2442feb95c99dc59372ff80fbfd639b2bf4018bc68ab0b549bd765aecf8230d7899c43f86cfcc7b6370c06c3232783e24f
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.12":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.8.3":
  version: 5.8.3
  resolution: "enhanced-resolve@npm:5.8.3"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: d79fbe531106448b768bb0673fb623ec0202d7ee70373ab7d4f4745d5dfe0806f38c9db7e7da8c941288fe475ab3d538db3791fce522056eeea40ca398c9e287
  languageName: node
  linkType: hard

"entities@npm:^1.1.1, entities@npm:~1.1.1":
  version: 1.1.2
  resolution: "entities@npm:1.1.2"
  checksum: d537b02799bdd4784ffd714d000597ed168727bddf4885da887c5a491d735739029a00794f1998abbf35f3f6aeda32ef5c15010dca1817d401903a501b6d3e05
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"entities@npm:^3.0.1":
  version: 3.0.1
  resolution: "entities@npm:3.0.1"
  checksum: aaf7f12033f0939be91f5161593f853f2da55866db55ccbf72f45430b8977e2b79dbd58c53d0fdd2d00bd7d313b75b0968d09f038df88e308aa97e39f9456572
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.19.1":
  version: 1.19.1
  resolution: "es-abstract@npm:1.19.1"
  dependencies:
    call-bind: ^1.0.2
    es-to-primitive: ^1.2.1
    function-bind: ^1.1.1
    get-intrinsic: ^1.1.1
    get-symbol-description: ^1.0.0
    has: ^1.0.3
    has-symbols: ^1.0.2
    internal-slot: ^1.0.3
    is-callable: ^1.2.4
    is-negative-zero: ^2.0.1
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.1
    is-string: ^1.0.7
    is-weakref: ^1.0.1
    object-inspect: ^1.11.0
    object-keys: ^1.1.1
    object.assign: ^4.1.2
    string.prototype.trimend: ^1.0.4
    string.prototype.trimstart: ^1.0.4
    unbox-primitive: ^1.0.1
  checksum: b6be8410672c5364db3fb01eb786e30c7b4bb32b4af63d381c08840f4382c4a168e7855cd338bf59d4f1a1a1138f4d748d1fd40ec65aaa071876f9e9fbfed949
  languageName: node
  linkType: hard

"es-module-lexer@npm:^0.9.0":
  version: 0.9.3
  resolution: "es-module-lexer@npm:0.9.3"
  checksum: 84bbab23c396281db2c906c766af58b1ae2a1a2599844a504df10b9e8dc77ec800b3211fdaa133ff700f5703d791198807bba25d9667392d27a5e9feda344da8
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-goat@npm:^2.0.0":
  version: 2.1.1
  resolution: "escape-goat@npm:2.1.1"
  checksum: ce05c70c20dd7007b60d2d644b625da5412325fdb57acf671ba06cb2ab3cd6789e2087026921a05b665b0a03fadee2955e7fc0b9a67da15a6551a980b260eba7
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3, escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^1.0.1":
  version: 1.0.1
  resolution: "estree-walker@npm:1.0.1"
  checksum: 7e70da539691f6db03a08e7ce94f394ce2eef4180e136d251af299d41f92fb2d28ebcd9a6e393e3728d7970aeb5358705ddf7209d52fbcb2dd4693f95dcf925f
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eta@npm:^1.12.3":
  version: 1.12.3
  resolution: "eta@npm:1.12.3"
  checksum: 390c1cd320755cb98fd5a4a911539e8ed498fc49b82414f0023033ff606d80a34e6df0aeeb9fb0b519b318a750e6d17a72fc25f8a8c686cfc52d638e998237a1
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"eval@npm:^0.1.4":
  version: 0.1.6
  resolution: "eval@npm:0.1.6"
  dependencies:
    require-like: ">= 0.1.1"
  checksum: 0e9246bb16256eef07afa7f31408310a784407d2fec2ddd2d7fe1f885a45b7cf37e30739e658a65d000c3dcff8d5b5c96f9819188b00e1f667b6638e75eaf23c
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"express@npm:^4.17.1":
  version: 4.17.2
  resolution: "express@npm:4.17.2"
  dependencies:
    accepts: ~1.3.7
    array-flatten: 1.1.1
    body-parser: 1.19.1
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.4.1
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: ~1.1.2
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: ~1.1.2
    fresh: 0.5.2
    merge-descriptors: 1.0.1
    methods: ~1.1.2
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    path-to-regexp: 0.1.7
    proxy-addr: ~2.0.7
    qs: 6.9.6
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.17.2
    serve-static: 1.14.2
    setprototypeof: 1.2.0
    statuses: ~1.5.0
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 1535d56d20e65a1a39b5f056c025dd635290a744478ac69cc47633aeb4b2ce51458f8eb4080cfb7ba47c853ba5cfd794d404cff822a25127f1556b726ec3914a
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: ^0.1.0
  checksum: 8fb58d9d7a511f4baf78d383e637bd7d2e80843bd9cd0853649108ea835208fb614da502a553acc30208e1325240bb7cc4a68473021612496bb89725483656d8
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.7, fast-glob@npm:^3.2.9":
  version: 3.2.10
  resolution: "fast-glob@npm:3.2.10"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: dee958d95638c8d00d200c55af5884dda513cfef96f674caab5a289a4436936ee26d603841a9ab85a6a4d9f7914558bce78dbf1088d3b8ec64b255422eea840b
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-url-parser@npm:1.1.3":
  version: 1.1.3
  resolution: "fast-url-parser@npm:1.1.3"
  dependencies:
    punycode: ^1.3.2
  checksum: 5043d0c4a8d775ff58504d56c096563c11b113e4cb8a2668c6f824a1cd4fb3812e2fdf76537eb24a7ce4ae7def6bd9747da630c617cf2a4b6ce0c42514e4f21c
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.13.0
  resolution: "fastq@npm:1.13.0"
  dependencies:
    reusify: ^1.0.4
  checksum: 32cf15c29afe622af187d12fc9cd93e160a0cb7c31a3bb6ace86b7dea3b28e7b72acde89c882663f307b2184e14782c6c664fa315973c03626c7d4bff070bb0b
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: ">=0.5.1"
  checksum: d49a62caf027f871149fc2b3f3c7104dc6d62744277eb6f9f36e2d5714e847d846b9f7f0d0b7169b25a012e24a594cde11a93034b30732e4c683f20b8a5019fa
  languageName: node
  linkType: hard

"fbemitter@npm:^3.0.0":
  version: 3.0.0
  resolution: "fbemitter@npm:3.0.0"
  dependencies:
    fbjs: ^3.0.0
  checksum: 069690b8cdff3521ade3c9beb92ba0a38d818a86ef36dff8690e66749aef58809db4ac0d6938eb1cacea2dbef5f2a508952d455669590264cdc146bbe839f605
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 72baf6d22c45b75109118b4daecb6c8016d4c83c8c0f23f683f22e9d7c21f32fff6201d288df46eb561e3c7d4bb4489b8ad140b7f56444c453ba407e8bd28511
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.0, fbjs@npm:^3.0.1":
  version: 3.0.2
  resolution: "fbjs@npm:3.0.2"
  dependencies:
    cross-fetch: ^3.0.4
    fbjs-css-vars: ^1.0.0
    loose-envify: ^1.0.0
    object-assign: ^4.1.0
    promise: ^7.1.1
    setimmediate: ^1.0.5
    ua-parser-js: ^0.7.30
  checksum: ebb1dc7a8caff563e4bf07b6e5e59a4052ea94d59faf73522b7e3ab20f7147c076aa565dfd04b648f5eb0ff357e1e18682dd17c490060b508f4fde8c70cfae06
  languageName: node
  linkType: hard

"feed@npm:^4.2.2":
  version: 4.2.2
  resolution: "feed@npm:4.2.2"
  dependencies:
    xml-js: ^1.6.11
  checksum: 2e6992a675a049511eef7bda8ca6c08cb9540cd10e8b275ec4c95d166228ec445a335fa8de990358759f248a92861e51decdcd32bf1c54737d5b7aed7c7ffe97
  languageName: node
  linkType: hard

"file-loader@npm:^6.2.0":
  version: 6.2.0
  resolution: "file-loader@npm:6.2.0"
  dependencies:
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: faf43eecf233f4897b0150aaa874eeeac214e4f9de49738a9e0ef734a30b5260059e85b7edadf852b98e415f875bd5f12587768a93fd52aaf2e479ecf95fab20
  languageName: node
  linkType: hard

"filelist@npm:^1.0.1":
  version: 1.0.2
  resolution: "filelist@npm:1.0.2"
  dependencies:
    minimatch: ^3.0.4
  checksum: 4d6953cb6f76c5345a52fc50222949e244946f485462ab6bae977176fff64fe5200cc1f44db175c27fc887f91cead401504c22eefcdcc064012ee44759947561
  languageName: node
  linkType: hard

"filesize@npm:^6.1.0":
  version: 6.4.0
  resolution: "filesize@npm:6.4.0"
  checksum: 83619b0a656225e84ba9a73271b80091629c0e88c2936c1ebd36fff96fb0e2fbae0273c2caccd522c02bc1a32ad9eba869c28c6b2c36e06187d25fd298c3dfe8
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"finalhandler@npm:~1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    statuses: ~1.5.0
    unpipe: ~1.0.0
  checksum: 617880460c5138dd7ccfd555cb5dde4d8f170f4b31b8bd51e4b646bb2946c30f7db716428a1f2882d730d2b72afb47d1f67cc487b874cb15426f95753a88965e
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^3.0.2
    pkg-dir: ^4.1.0
  checksum: 1e61c2e64f5c0b1c535bd85939ae73b0e5773142713273818cc0b393ee3555fb0fd44e1a5b161b8b6c3e03e98c2fcc9c227d784850a13a90a8ab576869576817
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flux@npm:^4.0.1":
  version: 4.0.3
  resolution: "flux@npm:4.0.3"
  dependencies:
    fbemitter: ^3.0.0
    fbjs: ^3.0.1
  peerDependencies:
    react: ^15.0.2 || ^16.0.0 || ^17.0.0
  checksum: 6b3f5150bcce481ce5bc09e54dbe4bf2a052f9fbc04c1de64f8d816adc4f90ad7955d9aed0022c7b6a2ed11b809ac40527bb50c2cd89c23d42f56694abe20748
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0, follow-redirects@npm:^1.14.0":
  version: 1.14.7
  resolution: "follow-redirects@npm:1.14.7"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: f6d03e5e30877431065bca0d1b2e3db93949eb799d368a5c07ea8a4b71205f0349a3f8f0191bf13a07c93885522834dca1dc8e527dc99a772c6911fba24edc5f
  languageName: node
  linkType: hard

"fork-ts-checker-webpack-plugin@npm:^6.0.5":
  version: 6.5.0
  resolution: "fork-ts-checker-webpack-plugin@npm:6.5.0"
  dependencies:
    "@babel/code-frame": ^7.8.3
    "@types/json-schema": ^7.0.5
    chalk: ^4.1.0
    chokidar: ^3.4.2
    cosmiconfig: ^6.0.0
    deepmerge: ^4.2.2
    fs-extra: ^9.0.0
    glob: ^7.1.6
    memfs: ^3.1.2
    minimatch: ^3.0.4
    schema-utils: 2.7.0
    semver: ^7.3.2
    tapable: ^1.0.0
  peerDependencies:
    eslint: ">= 6"
    typescript: ">= 2.7"
    vue-template-compiler: "*"
    webpack: ">= 4"
  peerDependenciesMeta:
    eslint:
      optional: true
    vue-template-compiler:
      optional: true
  checksum: 95d145ab7936445f3a9bfa4116ef73537f97196cfaa3f5b24473dff36d034e839d3b0e034a23beefc9619eceb7a9866816bfd55afd1968e955eb3b3f8cfc35ed
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fraction.js@npm:^4.1.2":
  version: 4.1.2
  resolution: "fraction.js@npm:4.1.2"
  checksum: a67eff2b599cb6546b77ce9c913bd0cccd646e1a525c793ba4e0bf5a399fc403f379227fca83423a6ea79d01e35c2f2b0f141ffa1d09e41377041268a53fb150
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.0.0
  resolution: "fs-extra@npm:10.0.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: 5285a3d8f34b917cf2b66af8c231a40c1623626e9d701a20051d3337be16c6d7cac94441c8b3732d47a92a2a027886ca93c69b6a4ae6aee3c89650d2a8880c0a
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0, fs-extra@npm:^9.0.1":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: ba71ba32e0faa74ab931b7a0031d1523c66a73e225de7426e275e238e312d07313d2da2d33e34a52aa406c8763ade5712eb3ec9ba4d9edce652bcacdc29e6b20
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-monkey@npm:1.0.3":
  version: 1.0.3
  resolution: "fs-monkey@npm:1.0.3"
  checksum: cf50804833f9b88a476911ae911fe50f61a98d986df52f890bd97e7262796d023698cb2309fa9b74fdd8974f04315b648748a0a8ee059e7d5257b293bfc409c0
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: latest
  checksum: 97ade64e75091afee5265e6956cb72ba34db7819b4c3e94c431d4be2b19b8bb7a2d4116da417950c3425f17c8fe693d25e20212cac583ac1521ad066b77ae31f
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#~builtin<compat/fsevents>::version=2.3.2&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"gauge@npm:^3.0.0":
  version: 3.0.2
  resolution: "gauge@npm:3.0.2"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.2
    console-control-strings: ^1.0.0
    has-unicode: ^2.0.1
    object-assign: ^4.1.1
    signal-exit: ^3.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.2
  checksum: 81296c00c7410cdd48f997800155fbead4f32e4f82109be0719c63edc8560e6579946cc8abd04205297640691ec26d21b578837fd13a4e96288ab4b40b1dc3e9
  languageName: node
  linkType: hard

"gauge@npm:^4.0.0":
  version: 4.0.0
  resolution: "gauge@npm:4.0.0"
  dependencies:
    ansi-regex: ^5.0.1
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.2
    console-control-strings: ^1.0.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.2
  checksum: 637b34c84f518defa89319dbef68211a24e9302182ad2a619e3be1be5b7dcf2a962c8359e889294af667440f4722e7e6e61671859e00bd8ec280a136ded89b25
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.1, gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.0, get-intrinsic@npm:^1.1.1":
  version: 1.1.1
  resolution: "get-intrinsic@npm:1.1.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-symbols: ^1.0.1
  checksum: a9fe2ca8fa3f07f9b0d30fb202bcd01f3d9b9b6b732452e79c48e79f7d6d8d003af3f9e38514250e3553fdc83c61650851cb6870832ac89deaaceb08e3721a17
  languageName: node
  linkType: hard

"get-own-enumerable-property-symbols@npm:^3.0.0":
  version: 3.0.2
  resolution: "get-own-enumerable-property-symbols@npm:3.0.2"
  checksum: 8f0331f14159f939830884799f937343c8c0a2c330506094bc12cbee3665d88337fe97a4ea35c002cc2bdba0f5d9975ad7ec3abb925015cdf2a93e76d4759ede
  languageName: node
  linkType: hard

"get-stream@npm:^4.1.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: ^3.0.0
  checksum: 443e1914170c15bd52ff8ea6eff6dfc6d712b031303e36302d2778e3de2506af9ee964d6124010f7818736dcfde05c04ba7ca6cc26883106e084357a17ae7d73
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 9ceff8fe968f9270a37a1f73bf3f1f7bda69ca80f4f80850670e0e7b9444ff99323f7ac52f96567f8b5f5fbe7ac717a0d81d3407c7313e82810c6199446a5247
  languageName: node
  linkType: hard

"github-slugger@npm:^1.4.0":
  version: 1.4.0
  resolution: "github-slugger@npm:1.4.0"
  checksum: 4f52e7a21f5c6a4c5328f01fe4fe13ae8881fea78bfe31f9e72c4038f97e3e70d52fb85aa7633a52c501dc2486874474d9abd22aa61cbe9b113099a495551c6b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.1":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.1.6":
  version: 7.2.0
  resolution: "glob@npm:7.2.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 78a8ea942331f08ed2e055cb5b9e40fe6f46f579d7fd3d694f3412fe5db23223d29b7fee1575440202e9a7ff9a72ab106a39fee39934c7bedafe5e5f8ae20134
  languageName: node
  linkType: hard

"global-dirs@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-dirs@npm:3.0.0"
  dependencies:
    ini: 2.0.0
  checksum: 953c17cf14bf6ee0e2100ae82a0d779934eed8a3ec5c94a7a4f37c5b3b592c31ea015fb9a15cf32484de13c79f4a814f3015152f3e1d65976cfbe47c1bfe4a88
  languageName: node
  linkType: hard

"global-modules@npm:^2.0.0":
  version: 2.0.0
  resolution: "global-modules@npm:2.0.0"
  dependencies:
    global-prefix: ^3.0.0
  checksum: d6197f25856c878c2fb5f038899f2dca7cbb2f7b7cf8999660c0104972d5cfa5c68b5a0a77fa8206bb536c3903a4615665acb9709b4d80846e1bb47eaef65430
  languageName: node
  linkType: hard

"global-prefix@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-prefix@npm:3.0.0"
  dependencies:
    ini: ^1.3.5
    kind-of: ^6.0.2
    which: ^1.3.1
  checksum: 8a82fc1d6f22c45484a4e34656cc91bf021a03e03213b0035098d605bfc612d7141f1e14a21097e8a0413b4884afd5b260df0b6a25605ce9d722e11f1df2881d
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globby@npm:^11.0.1, globby@npm:^11.0.2, globby@npm:^11.0.3, globby@npm:^11.0.4":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"got@npm:^9.6.0":
  version: 9.6.0
  resolution: "got@npm:9.6.0"
  dependencies:
    "@sindresorhus/is": ^0.14.0
    "@szmarczak/http-timer": ^1.1.2
    cacheable-request: ^6.0.0
    decompress-response: ^3.3.0
    duplexer3: ^0.1.4
    get-stream: ^4.1.0
    lowercase-keys: ^1.0.1
    mimic-response: ^1.0.1
    p-cancelable: ^1.0.0
    to-readable-stream: ^1.0.0
    url-parse-lax: ^3.0.0
  checksum: 941807bd9704bacf5eb401f0cc1212ffa1f67c6642f2d028fd75900471c221b1da2b8527f4553d2558f3faeda62ea1cf31665f8b002c6137f5de8732f07370b0
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.9
  resolution: "graceful-fs@npm:4.2.9"
  checksum: 68ea4e07ff2c041ada184f9278b830375f8e0b75154e3f080af6b70f66172fabb4108d19b3863a96b53fc068a310b9b6493d86d1291acc5f3861eb4b79d26ad6
  languageName: node
  linkType: hard

"gray-matter@npm:^4.0.3":
  version: 4.0.3
  resolution: "gray-matter@npm:4.0.3"
  dependencies:
    js-yaml: ^3.13.1
    kind-of: ^6.0.2
    section-matter: ^1.0.0
    strip-bom-string: ^1.0.0
  checksum: 37717bd424344487d655392251ce8d8878a1275ee087003e61208fba3bfd59cbb73a85b2159abf742ae95e23db04964813fdc33ae18b074208428b2528205222
  languageName: node
  linkType: hard

"gzip-size@npm:^5.1.1":
  version: 5.1.1
  resolution: "gzip-size@npm:5.1.1"
  dependencies:
    duplexer: ^0.1.1
    pify: ^4.0.1
  checksum: 6451ba2210877368f6d9ee9b4dc0d14501671472801323bf81fbd38bdeb8525f40a78be45a59d0182895d51e6b60c6314b7d02bd6ed40e7225a01e8d038aac1b
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 68071f313062315cd9dce55710e9496873945f1dd425107007058fc1629f93002a7649fcc3e464281ce02c7e809a35f5925504ab8105d972cf649f1f47cb7d6c
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-bigints@npm:1.0.1"
  checksum: 44ab55868174470065d2e0f8f6def1c990d12b82162a8803c679699fa8a39f966e336f2a33c185092fe8aea7e8bf2e85f1c26add5f29d98f2318bd270096b183
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.1, has-symbols@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-symbols@npm:1.0.2"
  checksum: 2309c426071731be792b5be43b3da6fb4ed7cbe8a9a6bcfca1862587709f01b33d575ce8f5c264c1eaad09fca2f9a8208c0a2be156232629daa2dd0c0740976b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has-yarn@npm:^2.1.0":
  version: 2.1.0
  resolution: "has-yarn@npm:2.1.0"
  checksum: 5eb1d0bb8518103d7da24532bdbc7124ffc6d367b5d3c10840b508116f2f1bcbcf10fd3ba843ff6e2e991bdf9969fd862d42b2ed58aade88343326c950b7e7f7
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hast-to-hyperscript@npm:^9.0.0":
  version: 9.0.1
  resolution: "hast-to-hyperscript@npm:9.0.1"
  dependencies:
    "@types/unist": ^2.0.3
    comma-separated-tokens: ^1.0.0
    property-information: ^5.3.0
    space-separated-tokens: ^1.0.0
    style-to-object: ^0.3.0
    unist-util-is: ^4.0.0
    web-namespaces: ^1.0.0
  checksum: de570d789853018fff2fd38fc096549b9814e366b298f60c90c159a57018230eefc44d46a246027b0e2426ed9e99f2e270050bc183d5bdfe4c9487c320b392cd
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^5.0.0":
  version: 5.0.3
  resolution: "hast-util-from-parse5@npm:5.0.3"
  dependencies:
    ccount: ^1.0.3
    hastscript: ^5.0.0
    property-information: ^5.0.0
    web-namespaces: ^1.1.2
    xtend: ^4.0.1
  checksum: 31ecd040dd03bda38b8efbcb93ed95b19619bc8548da19973b6cdbb36302bc54c84662be345e6a4f3a53cf8b33956b502916e349871dc095802ca39cfe55040a
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^6.0.0":
  version: 6.0.1
  resolution: "hast-util-from-parse5@npm:6.0.1"
  dependencies:
    "@types/parse5": ^5.0.0
    hastscript: ^6.0.0
    property-information: ^5.0.0
    vfile: ^4.0.0
    vfile-location: ^3.2.0
    web-namespaces: ^1.0.0
  checksum: 4daa78201468af7779161e7caa2513c329830778e0528481ab16b3e1bcef4b831f6285b526aacdddbee802f3bd9d64df55f80f010591ea1916da535e3a923b83
  languageName: node
  linkType: hard

"hast-util-has-property@npm:^1.0.0":
  version: 1.0.4
  resolution: "hast-util-has-property@npm:1.0.4"
  checksum: 23025cee6692cf9aaf70a369248901deff0886c9bd2e2f0e81735c5f67ff500d1cfd991d3c236fc1d43e02b29d2db4075ee9fd2fe0aea1a7da261f19195046e8
  languageName: node
  linkType: hard

"hast-util-is-element@npm:^1.0.0":
  version: 1.1.0
  resolution: "hast-util-is-element@npm:1.1.0"
  checksum: 30fad3f65e7ab2f0efd5db9e7344d0820b70971988dfe79f62d8447598b2a1ce8a59cd4bfc05ae0d9a1c451b9b53cbe1023743d7eac764d64720b6b73475f62f
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^2.0.0":
  version: 2.2.5
  resolution: "hast-util-parse-selector@npm:2.2.5"
  checksum: 22ee4afbd11754562144cb3c4f3ec52524dafba4d90ee52512902d17cf11066d83b38f7bdf6ca571bbc2541f07ba30db0d234657b6ecb8ca4631587466459605
  languageName: node
  linkType: hard

"hast-util-raw@npm:6.0.1":
  version: 6.0.1
  resolution: "hast-util-raw@npm:6.0.1"
  dependencies:
    "@types/hast": ^2.0.0
    hast-util-from-parse5: ^6.0.0
    hast-util-to-parse5: ^6.0.0
    html-void-elements: ^1.0.0
    parse5: ^6.0.0
    unist-util-position: ^3.0.0
    vfile: ^4.0.0
    web-namespaces: ^1.0.0
    xtend: ^4.0.0
    zwitch: ^1.0.0
  checksum: f6d960644f9fbbe0b92d0227b20a24d659cce021d5f9fd218e077154931b4524ee920217b7fd5a45ec2736ec1dee53de9209fe449f6f89454c01d225ff0e7851
  languageName: node
  linkType: hard

"hast-util-select@npm:^4.0.0":
  version: 4.0.2
  resolution: "hast-util-select@npm:4.0.2"
  dependencies:
    bcp-47-match: ^1.0.0
    comma-separated-tokens: ^1.0.0
    css-selector-parser: ^1.0.0
    direction: ^1.0.0
    hast-util-has-property: ^1.0.0
    hast-util-is-element: ^1.0.0
    hast-util-to-string: ^1.0.0
    hast-util-whitespace: ^1.0.0
    not: ^0.1.0
    nth-check: ^2.0.0
    property-information: ^5.0.0
    space-separated-tokens: ^1.0.0
    unist-util-visit: ^2.0.0
    zwitch: ^1.0.0
  checksum: ce02cfb8fda2c5dfc48884c99bc38b0e97e6d34debe0b71b78274896f2e1073971965cd54173ab87f31edfc1e60c4c4da0196a650c2eaa45140b5904edc646bf
  languageName: node
  linkType: hard

"hast-util-to-parse5@npm:^6.0.0":
  version: 6.0.0
  resolution: "hast-util-to-parse5@npm:6.0.0"
  dependencies:
    hast-to-hyperscript: ^9.0.0
    property-information: ^5.0.0
    web-namespaces: ^1.0.0
    xtend: ^4.0.0
    zwitch: ^1.0.0
  checksum: 91a36244e37df1d63c8b7e865ab0c0a25bb7396155602be005cf71d95c348e709568f80e0f891681a3711d733ad896e70642dc41a05b574eddf2e07d285408a8
  languageName: node
  linkType: hard

"hast-util-to-string@npm:^1.0.0":
  version: 1.0.4
  resolution: "hast-util-to-string@npm:1.0.4"
  checksum: 8132508d5c08d542b64979ad558e474f481011c29d5fce9b1a1fe779fc97e151b734b5c6d94f4937c4ec978b1d641977ee7f9f5ed61ea0611600764abdff7cbb
  languageName: node
  linkType: hard

"hast-util-to-text@npm:^2.0.0":
  version: 2.0.1
  resolution: "hast-util-to-text@npm:2.0.1"
  dependencies:
    hast-util-is-element: ^1.0.0
    repeat-string: ^1.0.0
    unist-util-find-after: ^3.0.0
  checksum: 4e7960b414b7a6b2f0180e4af416cd8ae3c7ba1531d7eaec7e6dc9509daf88308784bbf5b94885384dccc42abcb74cc6cc26755c76914d646f32aa6bc32ea34b
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^1.0.0":
  version: 1.0.4
  resolution: "hast-util-whitespace@npm:1.0.4"
  checksum: b7f4a1942bc78239a6fe4741aca34e3e7f84487e15e2cd2b6ca07bbba3055571763d877d7c077d7a2a029ede7500bc50a62af7b6dfe88e0644b16228b91dee0d
  languageName: node
  linkType: hard

"hastscript@npm:^5.0.0":
  version: 5.1.2
  resolution: "hastscript@npm:5.1.2"
  dependencies:
    comma-separated-tokens: ^1.0.0
    hast-util-parse-selector: ^2.0.0
    property-information: ^5.0.0
    space-separated-tokens: ^1.0.0
  checksum: 662321af446f09c76d67af31d05823f382ce1e6c007828dc77f899f310cea682c00216b67c317a4ebe7f0c05e50552c4810d214e6ed4e95388f7b7d7fc93158f
  languageName: node
  linkType: hard

"hastscript@npm:^6.0.0":
  version: 6.0.0
  resolution: "hastscript@npm:6.0.0"
  dependencies:
    "@types/hast": ^2.0.0
    comma-separated-tokens: ^1.0.0
    hast-util-parse-selector: ^2.0.0
    property-information: ^5.0.0
    space-separated-tokens: ^1.0.0
  checksum: 5e50b85af0d2cb7c17979cb1ddca75d6b96b53019dd999b39e7833192c9004201c3cee6445065620ea05d0087d9ae147a4844e582d64868be5bc6b0232dfe52d
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"history@npm:^4.9.0":
  version: 4.10.1
  resolution: "history@npm:4.10.1"
  dependencies:
    "@babel/runtime": ^7.1.2
    loose-envify: ^1.2.0
    resolve-pathname: ^3.0.0
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
    value-equal: ^1.0.1
  checksum: addd84bc4683929bae4400419b5af132ff4e4e9b311a0d4e224579ea8e184a6b80d7f72c55927e4fa117f69076a9e47ce082d8d0b422f1a9ddac7991490ca1d0
  languageName: node
  linkType: hard

"hogan.js@npm:^3.0.2":
  version: 3.0.2
  resolution: "hogan.js@npm:3.0.2"
  dependencies:
    mkdirp: 0.3.0
    nopt: 1.0.10
  bin:
    hulk: ./bin/hulk
  checksum: c7bbff84faa9ca265c39f4a2100546ba0388fcc9c5bac8526f488592ce3fcaa042eba6ac25db277f4478ec3855b9bc28ce59acffbf6e8a28d45a17df7590c6aa
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.0.0, hoist-non-react-statics@npm:^3.1.0":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: ^16.7.0
  checksum: b1538270429b13901ee586aa44f4cc3ecd8831c061d06cb8322e50ea17b3f5ce4d0e2e66394761e6c8e152cd8c34fb3b4b690116c6ce2bd45b18c746516cb9e8
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: ^2.0.1
    obuf: ^1.0.0
    readable-stream: ^2.0.1
    wbuf: ^1.1.0
  checksum: 2de144115197967ad6eeee33faf41096c6ba87078703c5cb011632dcfbffeb45784569e0cf02c317bd79c48375597c8ec88c30fff5bb0b023e8f654fb6e9c06e
  languageName: node
  linkType: hard

"html-entities@npm:^2.3.2":
  version: 2.3.2
  resolution: "html-entities@npm:2.3.2"
  checksum: 522d8d202df301ff51b517a379e642023ed5c81ea9fb5674ffad88cff386165733d00b6089d5c2fcc644e44777d6072017b6216d8fa40f271d3610420d00a886
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^6.0.2":
  version: 6.1.0
  resolution: "html-minifier-terser@npm:6.1.0"
  dependencies:
    camel-case: ^4.1.2
    clean-css: ^5.2.2
    commander: ^8.3.0
    he: ^1.2.0
    param-case: ^3.0.4
    relateurl: ^0.2.7
    terser: ^5.10.0
  bin:
    html-minifier-terser: cli.js
  checksum: ac52c14006476f773204c198b64838477859dc2879490040efab8979c0207424da55d59df7348153f412efa45a0840a1ca3c757bf14767d23a15e3e389d37a93
  languageName: node
  linkType: hard

"html-tags@npm:^3.1.0":
  version: 3.1.0
  resolution: "html-tags@npm:3.1.0"
  checksum: 67587f2d4022390d7bc34b1313773ecb0b0e0c79fb331aa3e20023eb4c862c7188a1ff775d126fcd75f4e4f08f956666a1c57688c4d24d85a77f9d4b1a42f345
  languageName: node
  linkType: hard

"html-void-elements@npm:^1.0.0":
  version: 1.0.5
  resolution: "html-void-elements@npm:1.0.5"
  checksum: 1a56f4f6cfbeb994c21701ff72b4b7f556fe784a70e5e554d1566ff775af83b91ea93f10664f039a67802d9f7b40d4a7f1ed20312bab47bd88d89bd792ea84ca
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:^5.4.0":
  version: 5.5.0
  resolution: "html-webpack-plugin@npm:5.5.0"
  dependencies:
    "@types/html-minifier-terser": ^6.0.0
    html-minifier-terser: ^6.0.2
    lodash: ^4.17.21
    pretty-error: ^4.0.0
    tapable: ^2.0.0
  peerDependencies:
    webpack: ^5.20.0
  checksum: f3d84d0df71fe2f5bac533cc74dce41ab058558cdcc6ff767d166a2abf1cf6fb8491d54d60ddbb34e95c00394e379ba52e0468e0284d1d0cc6a42987056e8219
  languageName: node
  linkType: hard

"htmlparser2@npm:^3.9.1":
  version: 3.10.1
  resolution: "htmlparser2@npm:3.10.1"
  dependencies:
    domelementtype: ^1.3.1
    domhandler: ^2.3.0
    domutils: ^1.5.1
    entities: ^1.1.1
    inherits: ^2.0.1
    readable-stream: ^3.1.1
  checksum: 6875f7dd875aa10be17d9b130e3738cd8ed4010b1f2edaf4442c82dfafe9d9336b155870dcc39f38843cbf7fef5e4fcfdf0c4c1fd4db3a1b91a1e0ee8f6c3475
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.0":
  version: 4.1.0
  resolution: "http-cache-semantics@npm:4.1.0"
  checksum: 974de94a81c5474be07f269f9fd8383e92ebb5a448208223bfb39e172a9dbc26feff250192ecc23b9593b3f92098e010406b0f24bd4d588d631f80214648ed42
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 64d7d1ae3a6933eb0e9a94e6f27be4af45a53a96c3c34e84ff57113787105a89fff9d1c3df263ef63add823df019b0e8f52f7121e32393bb5ce9a713bf100b41
  languageName: node
  linkType: hard

"http-errors@npm:1.8.1":
  version: 1.8.1
  resolution: "http-errors@npm:1.8.1"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: ">= 1.5.0 < 2"
    toidentifier: 1.0.1
  checksum: d3c7e7e776fd51c0a812baff570bdf06fe49a5dc448b700ab6171b1250e4cf7db8b8f4c0b133e4bfe2451022a5790c1ca6c2cae4094dedd6ac8304a1267f91d2
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.3
    setprototypeof: 1.1.0
    statuses: ">= 1.4.0 < 2"
  checksum: a9654ee027e3d5de305a56db1d1461f25709ac23267c6dc28cdab8323e3f96caa58a9a6a5e93ac15d7285cee0c2f019378c3ada9026e7fe19c872d695f27de7c
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.5
  resolution: "http-parser-js@npm:0.5.5"
  checksum: 85e67f12d99d67565be6c82dd86d4cf71939825fdf9826e10047b2443460bfef13235859ca67c0235d54e553db242204ec813febc86f11f83ed8ebd3cd475b65
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^4.0.1":
  version: 4.0.1
  resolution: "http-proxy-agent@npm:4.0.1"
  dependencies:
    "@tootallnate/once": 1
    agent-base: 6
    debug: 4
  checksum: c6a5da5a1929416b6bbdf77b1aca13888013fe7eb9d59fc292e25d18e041bb154a8dfada58e223fc7b76b9b2d155a87e92e608235201f77d34aa258707963a82
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:^2.0.0":
  version: 2.0.1
  resolution: "http-proxy-middleware@npm:2.0.1"
  dependencies:
    "@types/http-proxy": ^1.17.5
    http-proxy: ^1.18.1
    is-glob: ^4.0.1
    is-plain-obj: ^3.0.0
    micromatch: ^4.0.2
  checksum: 0de65bc6644b6efae5d26cd3bec071ceaeb92f26856ffee5ecdde9c702ea1435936e7dfb09da2ac0883eada80fdc993e9925902fc10bf6625565d6365f8cb30f
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: ^4.0.0
    follow-redirects: ^1.0.0
    requires-port: ^1.0.0
  checksum: f5bd96bf83e0b1e4226633dbb51f8b056c3e6321917df402deacec31dd7fe433914fc7a2c1831cf7ae21e69c90b3a669b8f434723e9e8b71fd68afe30737b6a5
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "https-proxy-agent@npm:5.0.0"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 165bfb090bd26d47693597661298006841ab733d0c7383a8cb2f17373387a94c903a3ac687090aa739de05e379ab6f868bae84ab4eac288ad85c328cd1ec9e53
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"idb@npm:^6.1.4":
  version: 6.1.5
  resolution: "idb@npm:6.1.5"
  checksum: 45d81be3bf5d5ae6d009d62b4a7eeb873fe2a9972d235aaa5c33cd3e27947b33a01fd3fb7bbdbe795cd608d2279c55ccd2db3f8b3f486bc74bdb5eab1c1be957
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.2.0
  resolution: "ignore@npm:5.2.0"
  checksum: 6b1f926792d614f64c6c83da3a1f9c83f6196c2839aa41e1e32dd7b8d174cef2e329d75caabb62cb61ce9dc432f75e67d07d122a037312db7caa73166a1bdb77
  languageName: node
  linkType: hard

"immediate@npm:^3.2.3":
  version: 3.3.0
  resolution: "immediate@npm:3.3.0"
  checksum: 634b4305101e2452eba6c07d485bf3e415995e533c94b9c3ffbc37026fa1be34def6e4f2276b0dc2162a3f91628564a4bfb26280278b89d3ee54624e854d2f5f
  languageName: node
  linkType: hard

"immer@npm:^9.0.6":
  version: 9.0.12
  resolution: "immer@npm:9.0.12"
  checksum: bcbec6d76dac65e49068eb67ece4d407116e62b8cde3126aa89c801e408f5047763ba0aeb62f1938c1aa704bb6612f1d8302bb2a86fa1fc60fcc12d8b25dc895
  languageName: node
  linkType: hard

"import-fresh@npm:^3.1.0, import-fresh@npm:^3.2.1, import-fresh@npm:^3.2.2, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-lazy@npm:^2.1.0":
  version: 2.1.0
  resolution: "import-lazy@npm:2.1.0"
  checksum: 05294f3b9dd4971d3a996f0d2f176410fb6745d491d6e73376429189f5c1c3d290548116b2960a7cf3e89c20cdf11431739d1d2d8c54b84061980795010e803a
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"infima@npm:0.2.0-alpha.37":
  version: 0.2.0-alpha.37
  resolution: "infima@npm:0.2.0-alpha.37"
  checksum: e62695838bb628a0a11ea126b4ede6984e26ad46f7170ea5325bae373c6be93ea3bd1f19c274ca8ab1549705cd07b677d73a71ffe7134f06450ca299edb4d68f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.0, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 78cb8d7d850d20a5e9a7f3620db31483aa00ad5f722ce03a55b110e5a723539b3716a3b463e2b96ce3fe286f33afc7c131fa2f91407528ba80cea98a7545d4c0
  languageName: node
  linkType: hard

"ini@npm:2.0.0":
  version: 2.0.0
  resolution: "ini@npm:2.0.0"
  checksum: e7aadc5fb2e4aefc666d74ee2160c073995a4061556b1b5b4241ecb19ad609243b9cceafe91bae49c219519394bbd31512516cb22a3b1ca6e66d869e0447e84e
  languageName: node
  linkType: hard

"ini@npm:^1.3.5, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.1.1":
  version: 0.1.1
  resolution: "inline-style-parser@npm:0.1.1"
  checksum: 5d545056a3e1f2bf864c928a886a0e1656a3517127d36917b973de581bd54adc91b4bf1febcb0da054f204b4934763f1a4e09308b4d55002327cf1d48ac5d966
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.3":
  version: 1.0.3
  resolution: "internal-slot@npm:1.0.3"
  dependencies:
    get-intrinsic: ^1.1.0
    has: ^1.0.3
    side-channel: ^1.0.4
  checksum: 1944f92e981e47aebc98a88ff0db579fd90543d937806104d0b96557b10c1f170c51fb777b97740a8b6ddeec585fca8c39ae99fd08a8e058dfc8ab70937238bf
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 2e5f51268b5941e4a17e4ef0575bc91ed0ab5f8515e3cf77486f7c14d13f3010df9c0959f37063dcc96e78d12dc6b0bb1b9e111cdfe69771f4656d2993d36155
  languageName: node
  linkType: hard

"ip@npm:^1.1.0, ip@npm:^1.1.5":
  version: 1.1.5
  resolution: "ip@npm:1.1.5"
  checksum: 30133981f082a060a32644f6a7746e9ba7ac9e2bc07ecc8bbdda3ee8ca9bec1190724c390e45a1ee7695e7edfd2a8f7dda2c104ec5f7ac5068c00648504c7e5a
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"ipaddr.js@npm:^2.0.1":
  version: 2.0.1
  resolution: "ipaddr.js@npm:2.0.1"
  checksum: dd194a394a843d470f88d17191b0948f383ed1c8e320813f850c336a0fcb5e9215d97ec26ca35ab4fbbd31392c8b3467f3e8344628029ed3710b2ff6b5d1034e
  languageName: node
  linkType: hard

"is-alphabetical@npm:1.0.4, is-alphabetical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphabetical@npm:1.0.4"
  checksum: 6508cce44fd348f06705d377b260974f4ce68c74000e7da4045f0d919e568226dc3ce9685c5a2af272195384df6930f748ce9213fc9f399b5d31b362c66312cb
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphanumerical@npm:1.0.4"
  dependencies:
    is-alphabetical: ^1.0.0
    is-decimal: ^1.0.0
  checksum: e2e491acc16fcf5b363f7c726f666a9538dba0a043665740feb45bba1652457a73441e7c5179c6768a638ed396db3437e9905f403644ec7c468fb41f4813d03f
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 7f02700ec2171b691ef3e4d0e3e6c0ba408e8434368504bb593d0d7c891c0dbfda6d19d30808b904a6cb1929bca648c061ba438c39f296c2a8ca083229c49f27
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.0":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 764c9ad8b523a9f5a32af29bdf772b08eb48c04d2ad0a7240916ac2688c983bf5f8504bf25b35e66240edeb9d9085461f9b5dae1f3d2861c6b06a65fe983de42
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.4, is-callable@npm:^1.2.4":
  version: 1.2.4
  resolution: "is-callable@npm:1.2.4"
  checksum: 1a28d57dc435797dae04b173b65d6d1e77d4f16276e9eff973f994eadcfdc30a017e6a597f092752a083c1103cceb56c91e3dadc6692fedb9898dfaba701575f
  languageName: node
  linkType: hard

"is-ci@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-ci@npm:2.0.0"
  dependencies:
    ci-info: ^2.0.0
  bin:
    is-ci: bin.js
  checksum: 77b869057510f3efa439bbb36e9be429d53b3f51abd4776eeea79ab3b221337fe1753d1e50058a9e2c650d38246108beffb15ccfd443929d77748d8c0cc90144
  languageName: node
  linkType: hard

"is-core-module@npm:^2.8.0":
  version: 2.8.1
  resolution: "is-core-module@npm:2.8.1"
  dependencies:
    has: ^1.0.3
  checksum: 418b7bc10768a73c41c7ef497e293719604007f88934a6ffc5f7c78702791b8528102fb4c9e56d006d69361549b3d9519440214a74aefc7e0b79e5e4411d377f
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-decimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-decimal@npm:1.0.4"
  checksum: ed483a387517856dc395c68403a10201fddcc1b63dc56513fbe2fe86ab38766120090ecdbfed89223d84ca8b1cd28b0641b93cb6597b6e8f4c097a7c24e3fb96
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 3875571d20a7563772ecc7a5f36cb03167e9be31ad259041b4a8f73f33f885441f778cee1f1fe0085eb4bc71679b9d8c923690003a36a6a5fdf8023e6e3f0672
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-hexadecimal@npm:1.0.4"
  checksum: a452e047587b6069332d83130f54d30da4faf2f2ebaa2ce6d073c27b5703d030d58ed9e0b729c8e4e5b52c6f1dab26781bb77b7bc6c7805f14f320e328ff8cd5
  languageName: node
  linkType: hard

"is-installed-globally@npm:^0.4.0":
  version: 0.4.0
  resolution: "is-installed-globally@npm:0.4.0"
  dependencies:
    global-dirs: ^3.0.0
    is-path-inside: ^3.0.2
  checksum: 3359840d5982d22e9b350034237b2cda2a12bac1b48a721912e1ab8e0631dd07d45a2797a120b7b87552759a65ba03e819f1bd63f2d7ab8657ec0b44ee0bf399
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-module@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-module@npm:1.0.0"
  checksum: 8cd5390730c7976fb4e8546dd0b38865ee6f7bacfa08dfbb2cc07219606755f0b01709d9361e01f13009bbbd8099fa2927a8ed665118a6105d66e40f1b838c3f
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: f3232194c47a549da60c3d509c9a09be442507616b69454716692e37ae9f37c4dea264fb208ad0c9f3efd15a796a46b79df07c7e53c6227c32170608b809149a
  languageName: node
  linkType: hard

"is-npm@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-npm@npm:5.0.0"
  checksum: 9baff02b0c69a3d3c79b162cb2f9e67fb40ef6d172c16601b2e2471c21e9a4fa1fc9885a308d7bc6f3a3cd2a324c27fa0bf284c133c3349bb22571ab70d041cc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.6
  resolution: "is-number-object@npm:1.0.6"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: c697704e8fc2027fc41cb81d29805de4e8b6dc9c3efee93741dbf126a8ecc8443fef85adbc581415ae7e55d325e51d0a942324ae35c829131748cce39cba55f3
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-obj@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-obj@npm:1.0.1"
  checksum: 3ccf0efdea12951e0b9c784e2b00e77e87b2f8bd30b42a498548a8afcc11b3287342a2030c308e473e93a7a19c9ea7854c99a8832a476591c727df2a9c79796c
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: c9916ac8f4621962a42f5e80e7ffdb1d79a3fab7456ceaeea394cd9e0858d04f985a9ace45be44433bf605673c8be8810540fe4cc7f4266fc7526ced95af5a08
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 46a840921bb8cc0dc7b5b423a14220e7db338072a4495743a8230533ce78812dc152548c86f4b828411fe98c5451959f07cf841c6a19f611e46600bd699e8048
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-plain-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-plain-obj@npm:3.0.0"
  checksum: a6ebdf8e12ab73f33530641972a72a4b8aed6df04f762070d823808303e4f76d87d5ea5bd76f96a7bbe83d93f04ac7764429c29413bd9049853a69cb630fb21c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-regex@npm:^1.0.4, is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-regexp@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-regexp@npm:1.0.0"
  checksum: be692828e24cba479ec33644326fa98959ec68ba77965e0291088c1a741feaea4919d79f8031708f85fd25e39de002b4520622b55460660b9c369e6f7187faef
  languageName: node
  linkType: hard

"is-root@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-root@npm:2.1.0"
  checksum: 37eea0822a2a9123feb58a9d101558ba276771a6d830f87005683349a9acff15958a9ca590a44e778c6b335660b83e85c744789080d734f6081a935a4880aee2
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-shared-array-buffer@npm:1.0.1"
  checksum: 2ffb92533e64e2876e6cfe6906871d28400b6f1a53130fe652ec8007bc0e5044d05e7af8e31bdc992fbba520bd92938cfbeedd0f286be92f250c7c76191c4d90
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-whitespace-character@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-whitespace-character@npm:1.0.4"
  checksum: adab8ad9847ccfcb6f1b7000b8f622881b5ba2a09ce8be2794a6d2b10c3af325b469fc562c9fb889f468eed27be06e227ac609d0aa1e3a59b4dbcc88e2b0418e
  languageName: node
  linkType: hard

"is-word-character@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-word-character@npm:1.0.4"
  checksum: 1821d6c6abe5bc0b3abe3fdc565d66d7c8a74ea4e93bc77b4a47d26e2e2a306d6ab7d92b353b0d2b182869e3ecaa8f4a346c62d0e31d38ebc0ceaf7cae182c3f
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"is-yarn-global@npm:^0.3.0":
  version: 0.3.0
  resolution: "is-yarn-global@npm:0.3.0"
  checksum: bca013d65fee2862024c9fbb3ba13720ffca2fe750095174c1c80922fdda16402b5c233f5ac9e265bc12ecb5446e7b7f519a32d9541788f01d4d44e24d2bf481
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 49191f1425681df4a18c2f0f93db3adb85573bcdd6a4482539d98eac9e705d8961317b01175627e860516a2fc45f8f9302db26e5a380a97a520e272e2a40a8d4
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"jake@npm:^10.6.1":
  version: 10.8.2
  resolution: "jake@npm:10.8.2"
  dependencies:
    async: 0.9.x
    chalk: ^2.4.2
    filelist: ^1.0.1
    minimatch: ^3.0.4
  bin:
    jake: ./bin/cli.js
  checksum: b604c51863260e374ccd62cd0cfe0b659f72cb71beb7d5fb5137dd65b04cf9d5603abd01f9f6eaaac8f4182f396d6cfae01e0b0844c2215c9c1e200572307cf9
  languageName: node
  linkType: hard

"jest-worker@npm:^26.2.1":
  version: 26.6.2
  resolution: "jest-worker@npm:26.6.2"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^7.0.0
  checksum: f9afa3b88e3f12027901e4964ba3ff048285b5783b5225cab28fac25b4058cea8ad54001e9a1577ee2bed125fac3ccf5c80dc507b120300cc1bbcb368796533e
  languageName: node
  linkType: hard

"jest-worker@npm:^27.0.2, jest-worker@npm:^27.4.1":
  version: 27.4.6
  resolution: "jest-worker@npm:27.4.6"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 105bcdf5c66700bbfe352bc09476629ca0858cfa819fcc1a37ea76660f0168d586c6e77aee8ea91eded5a20f40f331a0a81e503b5ba19f7b566204406b239466
  languageName: node
  linkType: hard

"joi@npm:^17.4.0, joi@npm:^17.4.2":
  version: 17.5.0
  resolution: "joi@npm:17.5.0"
  dependencies:
    "@hapi/hoek": ^9.0.0
    "@hapi/topo": ^5.0.0
    "@sideway/address": ^4.1.3
    "@sideway/formula": ^3.0.0
    "@sideway/pinpoint": ^2.0.0
  checksum: 6a20d009d2fa8a72dbfd9bc739d240f678b09d3a16c05b4bfb4e2d0503e60f7d7914250f0bfc52fb79a537490739ba36a1ace00a05b8ddecaaacfcedafc5c8b9
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"js-yaml@npm:^4.0.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.0":
  version: 3.0.0
  resolution: "json-buffer@npm:3.0.0"
  checksum: 0cecacb8025370686a916069a2ff81f7d55167421b6aa7270ee74e244012650dd6bce22b0852202ea7ff8624fce50ff0ec1bdf95914ccb4553426e290d5a63fa
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.2":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-schema@npm:^0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 66389434c3469e698da0df2e7ac5a3281bcff75e797a5c127db7c5b56270e01ae13d9afa3c03344f76e32e81678337a8c912bdbb75101c62e487dc3778461d72
  languageName: node
  linkType: hard

"json5@npm:^1.0.1":
  version: 1.0.1
  resolution: "json5@npm:1.0.1"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: e76ea23dbb8fc1348c143da628134a98adf4c5a4e8ea2adaa74a80c455fc2cdf0e2e13e6398ef819bfe92306b610ebb2002668ed9fc1af386d593691ef346fc3
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.0":
  version: 2.2.0
  resolution: "json5@npm:2.2.0"
  dependencies:
    minimist: ^1.2.5
  bin:
    json5: lib/cli.js
  checksum: e88fc5274bb58fc99547baa777886b069d2dd96d9cfc4490b305fd16d711dabd5979e35a4f90873cefbeb552e216b041a304fe56702bedba76e19bc7845f208d
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonpointer@npm:^5.0.0":
  version: 5.0.0
  resolution: "jsonpointer@npm:5.0.0"
  checksum: c7ec0b6bb596b81de687bc12945586bbcdc80dfb54919656d2690d76334f796a936270067ee9f1b5bbc2d9ecc551afb366ac35e6685aa61f07b5b68d1e5e857d
  languageName: node
  linkType: hard

"keyv@npm:^3.0.0":
  version: 3.1.0
  resolution: "keyv@npm:3.1.0"
  dependencies:
    json-buffer: 3.0.0
  checksum: bb7e8f3acffdbafbc2dd5b63f377fe6ec4c0e2c44fc82720449ef8ab54f4a7ce3802671ed94c0f475ae0a8549703353a2124561fcf3317010c141b32ca1ce903
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"klona@npm:^2.0.5":
  version: 2.0.5
  resolution: "klona@npm:2.0.5"
  checksum: 8c976126ea252b766e648a4866e1bccff9d3b08432474ad80c559f6c7265cf7caede2498d463754d8c88c4759895edd8210c85c0d3155e6aae4968362889466f
  languageName: node
  linkType: hard

"latest-version@npm:^5.1.0":
  version: 5.1.0
  resolution: "latest-version@npm:5.1.0"
  dependencies:
    package-json: ^6.3.0
  checksum: fbc72b071eb66c40f652441fd783a9cca62f08bf42433651937f078cd9ef94bf728ec7743992777826e4e89305aef24f234b515e6030503a2cbee7fc9bdc2c0f
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"lilconfig@npm:^2.0.3":
  version: 2.0.4
  resolution: "lilconfig@npm:2.0.4"
  checksum: 02ae530aa49218d782eb79e92c600ea5220828987f85aa3403fa512cadc7efe38c0ac7d0cd2edf600ad3fae1f6c1752f5b4bb78c0d9950435b044d53d507c9e1
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.2.0
  resolution: "loader-runner@npm:4.2.0"
  checksum: e61aea8b6904b8af53d9de6f0484da86c462c0001f4511bedc837cec63deb9475cea813db62f702cd7930420ccb0e75c78112270ca5c8b61b374294f53c0cb3a
  languageName: node
  linkType: hard

"loader-utils@npm:^1.4.0":
  version: 1.4.0
  resolution: "loader-utils@npm:1.4.0"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^1.0.1
  checksum: d150b15e7a42ac47d935c8b484b79e44ff6ab4c75df7cc4cb9093350cf014ec0b17bdb60c5d6f91a37b8b218bd63b973e263c65944f58ca2573e402b9a27e717
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.2
  resolution: "loader-utils@npm:2.0.2"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^2.1.2
  checksum: 9078d1ed47cadc57f4c6ddbdb2add324ee7da544cea41de3b7f1128e8108fcd41cd3443a85b7ee8d7d8ac439148aa221922774efe4cf87506d4fb054d5889303
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.assignin@npm:^4.0.9":
  version: 4.2.0
  resolution: "lodash.assignin@npm:4.2.0"
  checksum: 4b55bc1d65ccd7648fdba8a4316d10546929bf0beb5950830d86c559948cf170f0e65b77c95e66b45b511b85a31161714de8b2008d2537627ef3c7759afe36a6
  languageName: node
  linkType: hard

"lodash.bind@npm:^4.1.4":
  version: 4.2.1
  resolution: "lodash.bind@npm:4.2.1"
  checksum: cf0e41de2fca7704fc0adadc00f7fc871f8cf428990972f072136e4cd153c4d42d88c1418218121380914021c5547be05e4252e61f6280c736a2195cc8b6f4e5
  languageName: node
  linkType: hard

"lodash.curry@npm:^4.0.1":
  version: 4.1.1
  resolution: "lodash.curry@npm:4.1.1"
  checksum: 9192b70fe7df4d1ff780c0260bee271afa9168c93fe4fa24bc861900240531b59781b5fdaadf4644fea8f4fbcd96f0700539ab294b579ffc1022c6c15dcc462a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.0.1":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 84923258235592c8886e29de5491946ff8c2ae5c82a7ac5cddd2e3cb697e6fbdfbbb6efcca015795c86eec2bb953a5a2ee4016e3735a3f02720428a40efbb8f1
  languageName: node
  linkType: hard

"lodash.filter@npm:^4.4.0":
  version: 4.6.0
  resolution: "lodash.filter@npm:4.6.0"
  checksum: f21d245d24818e15b560cb6cadc8404a1bf98bd87d037e5e51858aad57ca2b9db64d87e450a23c8f72dd2c66968efd09b034055ce86d93eef4a4eb6f1bbaf100
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.2.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.flow@npm:^3.3.0":
  version: 3.5.0
  resolution: "lodash.flow@npm:3.5.0"
  checksum: a9a62ad344e3c5a1f42bc121da20f64dd855aaafecee24b1db640f29b88bd165d81c37ff7e380a7191de6f70b26f5918abcebbee8396624f78f3618a0b18634c
  languageName: node
  linkType: hard

"lodash.foreach@npm:^4.3.0":
  version: 4.5.0
  resolution: "lodash.foreach@npm:4.5.0"
  checksum: a940386b158ca0d62994db41fc16529eb8ae67138f29ced38e91f912cb5435d1b0ed34b18e6f7b9ddfc32ab676afc6dfec60d1e22633d8e3e4b33413402ab4ad
  languageName: node
  linkType: hard

"lodash.map@npm:^4.4.0":
  version: 4.6.0
  resolution: "lodash.map@npm:4.6.0"
  checksum: 7369a41d7d24d15ce3bbd02a7faa3a90f6266c38184e64932571b9b21b758bd10c04ffd117d1859be1a44156f29b94df5045eff172bf8a97fddf68bf1002d12f
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 9ff3942feeccffa4f1fafa88d32f0d24fdc62fd15ded5a74a5f950ff5f0c6f61916157246744c620173dddf38d37095a92327d5fd3861e2063e736a5c207d089
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.4.0":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.pick@npm:^4.2.1":
  version: 4.4.0
  resolution: "lodash.pick@npm:4.4.0"
  checksum: 2c36cab7da6b999a20bd3373b40e31a3ef81fa264f34a6979c852c5bc8ac039379686b27380f0cb8e3781610844fafec6949c6fbbebc059c98f8fa8570e3675f
  languageName: node
  linkType: hard

"lodash.reduce@npm:^4.4.0":
  version: 4.6.0
  resolution: "lodash.reduce@npm:4.6.0"
  checksum: 81f2a1045440554f8427f895ef479f1de5c141edd7852dde85a894879312801efae0295116e5cf830c531c1a51cdab8f3628c3ad39fa21a9874bb9158d9ea075
  languageName: node
  linkType: hard

"lodash.reject@npm:^4.4.0":
  version: 4.6.0
  resolution: "lodash.reject@npm:4.6.0"
  checksum: 730acc78d29ab0a60e0f3cd87bbfe9071625a835791ef66daac7a405c43ec21209fd795fdf9b7485aecead4869f645801bd65c27b9acadce80dee26393793111
  languageName: node
  linkType: hard

"lodash.some@npm:^4.4.0":
  version: 4.6.0
  resolution: "lodash.some@npm:4.6.0"
  checksum: 4469e76a389446d1166a29f844fb21398c36060d00258ce799710e046c55ed3c1af150c31b4856504e252bc813ba3fdcb6f255c490d9846738dd363a44665322
  languageName: node
  linkType: hard

"lodash.sortby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.sortby@npm:4.7.0"
  checksum: db170c9396d29d11fe9a9f25668c4993e0c1331bcb941ddbd48fb76f492e732add7f2a47cfdf8e9d740fa59ac41bbfaf931d268bc72aab3ab49e9f89354d718c
  languageName: node
  linkType: hard

"lodash.uniq@npm:4.5.0, lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: a4779b57a8d0f3c441af13d9afe7ecff22dd1b8ce1129849f71d9bbc8e8ee4e46dfb4b7c28f7ad3d67481edd6e51126e4e2a6ee276e25906d10f7140187c392d
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.19, lodash@npm:^4.17.20, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.2.0, loose-envify@npm:^1.3.1, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lowercase-keys@npm:^1.0.0, lowercase-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "lowercase-keys@npm:1.0.1"
  checksum: 4d045026595936e09953e3867722e309415ff2c80d7701d067546d75ef698dac218a4f53c6d1d0e7368b47e45fd7529df47e6cb56fbb90523ba599f898b3d147
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 24d7ebd56ccdf15ff529ca9e08863f3c54b0b9d1edb97a3ae1af34940ae666c01a1e6d200707bce730a8ef76cb57cc10e65f245ecaaf7e6bc8639f2fb460ac23
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lunr-languages@npm:^1.4.0":
  version: 1.9.0
  resolution: "lunr-languages@npm:1.9.0"
  checksum: 19da5df19ba77bc5b91653ed801676f2cb6d4f38dc5fd1d5446d909e4134c54e206f97715814abdeb6792bd4e21915259a8c3e02b3a8727ec8545f2a6d13dd11
  languageName: node
  linkType: hard

"lunr@npm:^2.3.8, lunr@npm:^2.3.9":
  version: 2.3.9
  resolution: "lunr@npm:2.3.9"
  checksum: 176719e24fcce7d3cf1baccce9dd5633cd8bdc1f41ebe6a180112e5ee99d80373fe2454f5d4624d437e5a8319698ca6837b9950566e15d2cae5f2a543a3db4b8
  languageName: node
  linkType: hard

"magic-string@npm:^0.25.0, magic-string@npm:^0.25.1, magic-string@npm:^0.25.2, magic-string@npm:^0.25.3, magic-string@npm:^0.25.7":
  version: 0.25.7
  resolution: "magic-string@npm:0.25.7"
  dependencies:
    sourcemap-codec: ^1.4.4
  checksum: 727a1fb70f9610304fe384f1df0251eb7d1d9dd779c07ef1225690361b71b216f26f5d934bfb11c919b5b0e7ba50f6240c823a6f2e44cfd33d4a07d7747ca829
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.0, make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^9.1.0":
  version: 9.1.0
  resolution: "make-fetch-happen@npm:9.1.0"
  dependencies:
    agentkeepalive: ^4.1.3
    cacache: ^15.2.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^4.0.1
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^6.0.0
    minipass: ^3.1.3
    minipass-collect: ^1.0.2
    minipass-fetch: ^1.3.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.2
    promise-retry: ^2.0.1
    socks-proxy-agent: ^6.0.0
    ssri: ^8.0.0
  checksum: 0eb371c85fdd0b1584fcfdf3dc3c62395761b3c14658be02620c310305a9a7ecf1617a5e6fb30c1d081c5c8aaf177fa133ee225024313afabb7aa6a10f1e3d04
  languageName: node
  linkType: hard

"markdown-escapes@npm:^1.0.0":
  version: 1.0.4
  resolution: "markdown-escapes@npm:1.0.4"
  checksum: 6833a93d72d3f70a500658872312c6fa8015c20cc835a85ae6901fa232683fbc6ed7118ebe920fea7c80039a560f339c026597d96eee0e9de602a36921804997
  languageName: node
  linkType: hard

"mdast-squeeze-paragraphs@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-squeeze-paragraphs@npm:4.0.0"
  dependencies:
    unist-util-remove: ^2.0.0
  checksum: dfe8ec8e8a62171f020e82b088cc35cb9da787736dc133a3b45ce8811782a93e69bf06d147072e281079f09fac67be8a36153ffffd9bfbf89ed284e4c4f56f75
  languageName: node
  linkType: hard

"mdast-util-definitions@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-util-definitions@npm:4.0.0"
  dependencies:
    unist-util-visit: ^2.0.0
  checksum: 2325f20b82b3fb8cb5fda77038ee0bbdd44f82cfca7c48a854724b58bc1fe5919630a3ce7c45e210726df59d46c881d020b2da7a493bfd1ee36eb2bbfef5d78e
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:10.0.1":
  version: 10.0.1
  resolution: "mdast-util-to-hast@npm:10.0.1"
  dependencies:
    "@types/mdast": ^3.0.0
    "@types/unist": ^2.0.0
    mdast-util-definitions: ^4.0.0
    mdurl: ^1.0.0
    unist-builder: ^2.0.0
    unist-util-generated: ^1.0.0
    unist-util-position: ^3.0.0
    unist-util-visit: ^2.0.0
  checksum: e5f385757df7e9b37db4d6f326bf7b4fc1b40f9ad01fc335686578f44abe0ba46d3e60af4d5e5b763556d02e65069ef9a09c49db049b52659203a43e7fa9084d
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-to-string@npm:2.0.0"
  checksum: 0b2113ada10e002fbccb014170506dabe2f2ddacaacbe4bc1045c33f986652c5a162732a2c057c5335cdb58419e2ad23e368e5be226855d4d4e280b81c4e9ec2
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"mdurl@npm:^1.0.0":
  version: 1.0.1
  resolution: "mdurl@npm:1.0.1"
  checksum: 71731ecba943926bfbf9f9b51e28b5945f9411c4eda80894221b47cc105afa43ba2da820732b436f0798fd3edbbffcd1fc1415843c41a87fea08a41cc1e3d02b
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"memfs@npm:^3.1.2, memfs@npm:^3.2.2":
  version: 3.4.1
  resolution: "memfs@npm:3.4.1"
  dependencies:
    fs-monkey: 1.0.3
  checksum: 6d2f49d447d1be24ff9c747618933784eeb059189bc6a0d77b7a51c7daf06e2d3a74674a2e2ff1520e2c312bf91e719ed37144cf05087379b3ba0aef0b6aa062
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 5abc259d2ae25bb06d19ce2b94a21632583c74e2a9109ee1ba7fd147aa7362b380d971e0251069f8b3eb7d48c21ac839e21fa177b335e82c76ec172e30c31a26
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4":
  version: 4.0.4
  resolution: "micromatch@npm:4.0.4"
  dependencies:
    braces: ^3.0.1
    picomatch: ^2.2.3
  checksum: ef3d1c88e79e0a68b0e94a03137676f3324ac18a908c245a9e5936f838079fcc108ac7170a5fadc265a9c2596963462e402841406bda1a4bb7b68805601d631c
  languageName: node
  linkType: hard

"mime-db@npm:1.51.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.51.0
  resolution: "mime-db@npm:1.51.0"
  checksum: 613b1ac9d6e725cc24444600b124a7f1ce6c60b1baa654f39a3e260d0995a6dffc5693190217e271af7e2a5612dae19f2a73f3e316707d797a7391165f7ef423
  languageName: node
  linkType: hard

"mime-db@npm:~1.33.0":
  version: 1.33.0
  resolution: "mime-db@npm:1.33.0"
  checksum: 281a0772187c9b8f6096976cb193ac639c6007ac85acdbb8dc1617ed7b0f4777fa001d1b4f1b634532815e60717c84b2f280201d55677fb850c9d45015b50084
  languageName: node
  linkType: hard

"mime-types@npm:2.1.18":
  version: 2.1.18
  resolution: "mime-types@npm:2.1.18"
  dependencies:
    mime-db: ~1.33.0
  checksum: 729265eff1e5a0e87cb7f869da742a610679585167d2f2ec997a7387fc6aedf8e5cad078e99b0164a927bdf3ace34fca27430d6487456ad090cba5594441ba43
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27, mime-types@npm:^2.1.31, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24":
  version: 2.1.34
  resolution: "mime-types@npm:2.1.34"
  dependencies:
    mime-db: 1.51.0
  checksum: 67013de9e9d6799bde6d669d18785b7e18bcd212e710d3e04a4727f92f67a8ad4e74aee24be28b685adb794944814bde649119b58ee3282ffdbee58f9278d9f3
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0, mimic-response@npm:^1.0.1":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mini-create-react-context@npm:^0.4.0":
  version: 0.4.1
  resolution: "mini-create-react-context@npm:0.4.1"
  dependencies:
    "@babel/runtime": ^7.12.1
    tiny-warning: ^1.0.3
  peerDependencies:
    prop-types: ^15.0.0
    react: ^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: f8cb2c7738aac355fe9ce7e8425f371b7fa90daddd5133edda4ccfdc18c49043b2ec04be6f3abf09b60a0f52549d54f158d5bfd81cdfb1a658531e5b9fe7bc6a
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^1.6.0":
  version: 1.6.2
  resolution: "mini-css-extract-plugin@npm:1.6.2"
  dependencies:
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
    webpack-sources: ^1.1.0
  peerDependencies:
    webpack: ^4.4.0 || ^5.0.0
  checksum: c2c1f3d7e5bc206b5bece0f37b65467ee58e0bb0b61d5e031bb818682b02db2552b296f5018af9058b18eb7127e00f6462fb718712a3d4432079dfa848b510cc
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimatch@npm:3.0.4, minimatch@npm:^3.0.4":
  version: 3.0.4
  resolution: "minimatch@npm:3.0.4"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: 66ac295f8a7b59788000ea3749938b0970344c841750abd96694f80269b926ebcafad3deeb3f1da2522978b119e6ae3a5869b63b13a7859a456b3408bd18a078
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5":
  version: 1.2.5
  resolution: "minimist@npm:1.2.5"
  checksum: 86706ce5b36c16bfc35c5fe3dbb01d5acdc9a22f2b6cc810b6680656a1d2c0e44a0159c9a3ba51fb072bb5c203e49e10b51dcd0eec39c481f4c42086719bae52
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^1.3.2":
  version: 1.4.1
  resolution: "minipass-fetch@npm:1.4.1"
  dependencies:
    encoding: ^0.1.12
    minipass: ^3.1.0
    minipass-sized: ^1.0.3
    minizlib: ^2.0.0
  dependenciesMeta:
    encoding:
      optional: true
  checksum: ec93697bdb62129c4e6c0104138e681e30efef8c15d9429dd172f776f83898471bc76521b539ff913248cc2aa6d2b37b652c993504a51cc53282563640f29216
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.2, minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.0, minipass@npm:^3.1.1, minipass@npm:^3.1.3":
  version: 3.1.6
  resolution: "minipass@npm:3.1.6"
  dependencies:
    yallist: ^4.0.0
  checksum: 57a04041413a3531a65062452cb5175f93383ef245d6f4a2961d34386eb9aa8ac11ac7f16f791f5e8bbaf1dfb1ef01596870c88e8822215db57aa591a5bb0a77
  languageName: node
  linkType: hard

"minizlib@npm:^2.0.0, minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp@npm:0.3.0":
  version: 0.3.0
  resolution: "mkdirp@npm:0.3.0"
  checksum: 3ec9cda8bd89b64892728e5092bc79e88382e444d4bbde040c2fb8d7034dc70682cfdd729e93241fd5243d2397324c420ef68c717d806db51bf96c0fc80f4b1d
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.5":
  version: 0.5.5
  resolution: "mkdirp@npm:0.5.5"
  dependencies:
    minimist: ^1.2.5
  bin:
    mkdirp: bin/cmd.js
  checksum: 3bce20ea525f9477befe458ab85284b0b66c8dc3812f94155af07c827175948cdd8114852ac6c6d82009b13c1048c37f6d98743eb019651ee25c39acc8aabe7d
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mrmime@npm:^1.0.0":
  version: 1.0.0
  resolution: "mrmime@npm:1.0.0"
  checksum: 2c72a40942af7c53bc97d1e9e9c5cb0e6541d18f736811c3a1b46fa2a2b2362480d687daa8ae8372523acaacd82426a4f7ce34b0bf1825ea83b3983e8cb91afd
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multicast-dns-service-types@npm:^1.1.0":
  version: 1.1.0
  resolution: "multicast-dns-service-types@npm:1.1.0"
  checksum: 0979fca1cce85484d256e4db3af591d941b41a61f134da3607213d2624c12ed5b8a246565cb19a9b3cb542819e8fbc71a90b07e77023ee6a9515540fe1d371f7
  languageName: node
  linkType: hard

"multicast-dns@npm:^6.0.1":
  version: 6.2.3
  resolution: "multicast-dns@npm:6.2.3"
  dependencies:
    dns-packet: ^1.3.1
    thunky: ^1.0.2
  bin:
    multicast-dns: cli.js
  checksum: f515b49ca964429ab48a4ac8041fcf969c927aeb49ab65288bd982e52c849a870fc3b03565780b0d194a1a02da8821f28b6425e48e95b8107bc9fcc92f571a6f
  languageName: node
  linkType: hard

"nanoid@npm:^3.1.30":
  version: 3.1.32
  resolution: "nanoid@npm:3.1.32"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 7389375c7d9819609366cc7b2bd446c854762617a626d345e5dcdc6629f6571e071c7ba412920148065dbd939dbc5589f9e2734b244c3cb49bbddc96082adcc1
  languageName: node
  linkType: hard

"negotiator@npm:0.6.2, negotiator@npm:^0.6.2":
  version: 0.6.2
  resolution: "negotiator@npm:0.6.2"
  checksum: dfddaff6c06792f1c4c3809e29a427b8daef8cd437c83b08dd51d7ee11bbd1c29d9512d66b801144d6c98e910ffd8723f2432e0cbf8b18d41d2a09599c975ab3
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-emoji@npm:^1.10.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: ^4.17.21
  checksum: e8c856c04a1645062112a72e59a98b203505ed5111ff84a3a5f40611afa229b578c7d50f1e6a7f17aa62baeea4a640d2e2f61f63afc05423aa267af10977fb2b
  languageName: node
  linkType: hard

"node-fetch@npm:2.6.1":
  version: 2.6.1
  resolution: "node-fetch@npm:2.6.1"
  checksum: 91075bedd57879117e310fbcc36983ad5d699e522edb1ebcdc4ee5294c982843982652925c3532729fdc86b2d64a8a827797a745f332040d91823c8752ee4d7c
  languageName: node
  linkType: hard

"node-forge@npm:^1.2.0":
  version: 1.2.1
  resolution: "node-forge@npm:1.2.1"
  checksum: af4f88c3f69362359f35f6a9e231b35c96d906eeb6e976fb92742afe7fcdd76439dc22b41ce3755389d171f6320756ec7505bdfa7b252466c091b8c519a22674
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 8.4.1
  resolution: "node-gyp@npm:8.4.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^9.1.0
    nopt: ^5.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 341710b5da39d3660e6a886b37e210d33f8282047405c2e62c277bcc744c7552c5b8b972ebc3a7d5c2813794e60cc48c3ebd142c46d6e0321db4db6c92dd0355
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.1":
  version: 2.0.1
  resolution: "node-releases@npm:2.0.1"
  checksum: b20dd8d4bced11f75060f0387e05e76b9dc4a0451f7bb3516eade6f50499ea7768ba95d8a60d520c193402df1e58cb3fe301510cc1c1ad68949c3d57b5149866
  languageName: node
  linkType: hard

"nopt@npm:1.0.10":
  version: 1.0.10
  resolution: "nopt@npm:1.0.10"
  dependencies:
    abbrev: 1
  bin:
    nopt: ./bin/nopt.js
  checksum: f62575aceaa3be43f365bf37a596b89bbac2e796b001b6d2e2a85c2140a4e378ff919e2753ccba959c4fd344776fc88c29b393bc167fa939fb1513f126f4cd45
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: 1
  bin:
    nopt: bin/nopt.js
  checksum: d35fdec187269503843924e0114c0c6533fb54bbf1620d0f28b4b60ba01712d6687f62565c55cc20a504eff0fbe5c63e22340c3fad549ad40469ffb611b04f2f
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^4.1.0":
  version: 4.5.1
  resolution: "normalize-url@npm:4.5.1"
  checksum: 9a9dee01df02ad23e171171893e56e22d752f7cff86fb96aafeae074819b572ea655b60f8302e2d85dbb834dc885c972cc1c573892fea24df46b2765065dd05a
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 4a4944631173e7d521d6b80e4c85ccaeceb2870f315584fa30121f505a6dfd86439c5e3fdd8cd9e0e291290c41d0c3599f0cb12ab356722ed242584c30348e50
  languageName: node
  linkType: hard

"not@npm:^0.1.0":
  version: 0.1.0
  resolution: "not@npm:0.1.0"
  checksum: 8043bb53bc1c465a4a4f751394f11aad1d8ccae08dd2123310c6a5d160a5ad4138706d50af905cf114b72507a328585d4f2a73cd3d6730981dd2675aa9c8436f
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.0
  resolution: "npmlog@npm:6.0.0"
  dependencies:
    are-we-there-yet: ^2.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.0
    set-blocking: ^2.0.0
  checksum: 33d8a7fe3d63bf83b16655b6588ae7ba10b5f37b067a661e7cab6508660d7c3204ae716ee2c5ce4eb9626fd1489cf2fa7645d789bc3b704f8c3ccb04a532a50b
  languageName: node
  linkType: hard

"nprogress@npm:^0.2.0":
  version: 0.2.0
  resolution: "nprogress@npm:0.2.0"
  checksum: 66b7bec5d563ecf2d1c3d2815e6d5eb74ed815eee8563e0afa63d3f185ab1b9cf2ddd97e1ded263b9995c5019d26d600320e849e50f3747984daa033744619dc
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.0, nth-check@npm:^2.0.1":
  version: 2.0.1
  resolution: "nth-check@npm:2.0.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5386d035c48438ff304fe687704d93886397349d1bed136de97aeae464caba10e8ffac55a04b215b86b3bc8897f33e0a5aa1045a9d8b2f251ae61b2a3ad3e450
  languageName: node
  linkType: hard

"nth-check@npm:~1.0.1":
  version: 1.0.2
  resolution: "nth-check@npm:1.0.2"
  dependencies:
    boolbase: ~1.0.0
  checksum: 59e115fdd75b971d0030f42ada3aac23898d4c03aa13371fa8b3339d23461d1badf3fde5aad251fb956aaa75c0a3b9bfcd07c08a34a83b4f9dadfdce1d19337c
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.11.0, object-inspect@npm:^1.9.0":
  version: 1.12.0
  resolution: "object-inspect@npm:1.12.0"
  checksum: 2b36d4001a9c921c6b342e2965734519c9c58c355822243c3207fbf0aac271f8d44d30d2d570d450b2cc6f0f00b72bcdba515c37827d2560e5f22b1899a31cf4
  languageName: node
  linkType: hard

"object-is@npm:^1.0.1":
  version: 1.1.5
  resolution: "object-is@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 989b18c4cba258a6b74dc1d74a41805c1a1425bce29f6cabb50dcb1a6a651ea9104a1b07046739a49a5bb1bc49727bcb00efd5c55f932f6ea04ec8927a7901fe
  languageName: node
  linkType: hard

"object-keys@npm:^1.0.12, object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.0, object.assign@npm:^4.1.2":
  version: 4.1.2
  resolution: "object.assign@npm:4.1.2"
  dependencies:
    call-bind: ^1.0.0
    define-properties: ^1.1.3
    has-symbols: ^1.0.1
    object-keys: ^1.1.1
  checksum: d621d832ed7b16ac74027adb87196804a500d80d9aca536fccb7ba48d33a7e9306a75f94c1d29cbfa324bc091bfc530bc24789568efdaee6a47fcfa298993814
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 41a2ba310e7b6f6c3b905af82c275bf8854896e2e4c5752966d64cbcd2f599cfffd5932006bcf3b8b419dfdacebb3a3912d5d94e10f1d0acab59876c8757f27f
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: 1.1.1
  checksum: 1db595bd963b0124d6fa261d18320422407b8f01dc65863840f3ddaaf7bcad5b28ff6847286703ca53f4ec19595bd67a2f1253db79fc4094911ec6aa8df1671b
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"open@npm:^7.0.2":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: ^2.0.0
    is-wsl: ^2.1.1
  checksum: 3333900ec0e420d64c23b831bc3467e57031461d843c801f569b2204a1acc3cd7b3ec3c7897afc9dde86491dfa289708eb92bba164093d8bd88fb2c231843c91
  languageName: node
  linkType: hard

"open@npm:^8.0.9":
  version: 8.4.0
  resolution: "open@npm:8.4.0"
  dependencies:
    define-lazy-prop: ^2.0.0
    is-docker: ^2.1.1
    is-wsl: ^2.2.0
  checksum: e9545bec64cdbf30a0c35c1bdc310344adf8428a117f7d8df3c0af0a0a24c513b304916a6d9b11db0190ff7225c2d578885080b761ed46a3d5f6f1eebb98b63c
  languageName: node
  linkType: hard

"opener@npm:^1.5.2":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"os-homedir@npm:^1.0.1":
  version: 1.0.2
  resolution: "os-homedir@npm:1.0.2"
  checksum: af609f5a7ab72de2f6ca9be6d6b91a599777afc122ac5cad47e126c1f67c176fe9b52516b9eeca1ff6ca0ab8587fe66208bc85e40a3940125f03cdb91408e9d2
  languageName: node
  linkType: hard

"p-cancelable@npm:^1.0.0":
  version: 1.1.0
  resolution: "p-cancelable@npm:1.1.0"
  checksum: 2db3814fef6d9025787f30afaee4496a8857a28be3c5706432cbad76c688a6db1874308f48e364a42f5317f5e41e8e7b4f2ff5c8ff2256dbb6264bc361704ece
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-retry@npm:^4.5.0":
  version: 4.6.1
  resolution: "p-retry@npm:4.6.1"
  dependencies:
    "@types/retry": ^0.12.0
    retry: ^0.13.1
  checksum: e6d540413bb3d0b96e0db44f74a7af1dce41f5005e6e84d617960110b148348c86a3987be07797749e3ddd55817dd3a8ffd6eae3428758bc2994d987e48c3a70
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json@npm:^6.3.0":
  version: 6.5.0
  resolution: "package-json@npm:6.5.0"
  dependencies:
    got: ^9.6.0
    registry-auth-token: ^4.0.0
    registry-url: ^5.0.0
    semver: ^6.2.0
  checksum: cc9f890d3667d7610e6184decf543278b87f657d1ace0deb4a9c9155feca738ef88f660c82200763d3348010f4e42e9c7adc91e96ab0f86a770955995b5351e2
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-entities@npm:^2.0.0":
  version: 2.0.0
  resolution: "parse-entities@npm:2.0.0"
  dependencies:
    character-entities: ^1.0.0
    character-entities-legacy: ^1.0.0
    character-reference-invalid: ^1.0.0
    is-alphanumerical: ^1.0.0
    is-decimal: ^1.0.0
    is-hexadecimal: ^1.0.0
  checksum: 7addfd3e7d747521afac33c8121a5f23043c6973809756920d37e806639b4898385d386fcf4b3c8e2ecf1bc28aac5ae97df0b112d5042034efbe80f44081ebce
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse-numeric-range@npm:^1.3.0":
  version: 1.3.0
  resolution: "parse-numeric-range@npm:1.3.0"
  checksum: 289ca126d5b8ace7325b199218de198014f58ea6895ccc88a5247491d07f0143bf047f80b4a31784f1ca8911762278d7d6ecb90a31dfae31da91cc1a2524c8ce
  languageName: node
  linkType: hard

"parse5@npm:^5.0.0":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: 613a714af4c1101d1cb9f7cece2558e35b9ae8a0c03518223a4a1e35494624d9a9ad5fad4c13eab66a0e0adccd9aa3d522fc8f5f9cc19789e0579f3fa0bdfc65
  languageName: node
  linkType: hard

"parse5@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 7d569a176c5460897f7c8f3377eff640d54132b9be51ae8a8fa4979af940830b2b0c296ce75e5bd8f4041520aadde13170dbdec44889975f906098ea0002f4bd
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-is-inside@npm:1.0.2":
  version: 1.0.2
  resolution: "path-is-inside@npm:1.0.2"
  checksum: 0b5b6c92d3018b82afb1f74fe6de6338c4c654de4a96123cb343f2b747d5606590ac0c890f956ed38220a4ab59baddfd7b713d78a62d240b20b14ab801fa02cb
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 69a14ea24db543e8b0f4353305c5eac6907917031340e5a8b37df688e52accd09e3cebfe1660b70d76b6bd89152f52183f28c74813dbf454ba1a01c82a38abce
  languageName: node
  linkType: hard

"path-to-regexp@npm:2.2.1":
  version: 2.2.1
  resolution: "path-to-regexp@npm:2.2.1"
  checksum: b921a74e7576e25b06ad1635abf7e8125a29220d2efc2b71d74b9591f24a27e6f09078fa9a1b27516a097ea0637b7cab79d19b83d7f36a8ef3ef5422770e89d9
  languageName: node
  linkType: hard

"path-to-regexp@npm:^1.7.0":
  version: 1.8.0
  resolution: "path-to-regexp@npm:1.8.0"
  dependencies:
    isarray: 0.0.1
  checksum: 709f6f083c0552514ef4780cb2e7e4cf49b0cc89a97439f2b7cc69a608982b7690fb5d1720a7473a59806508fc2dae0be751ba49f495ecf89fd8fbc62abccbcd
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pianorhythm-docs@workspace:.":
  version: 0.0.0-use.local
  resolution: "pianorhythm-docs@workspace:."
  dependencies:
    "@docusaurus/core": 2.0.0-beta.14
    "@docusaurus/module-type-aliases": ^2.0.0-beta.14
    "@docusaurus/plugin-pwa": ^2.0.0-beta.14
    "@docusaurus/preset-classic": 2.0.0-beta.14
    "@docusaurus/theme-live-codeblock": 2.0.0-beta.14
    "@mdx-js/react": ^1.6.21
    "@tsconfig/docusaurus": ^1.0.4
    acorn: ^8.7.0
    autocomplete.js: ^0.38.1
    classnames: ^2.3.1
    clsx: ^1.1.1
    cross-env: ^7.0.3
    docusaurus-lunr-search: ^2.1.15
    hogan.js: ^3.0.2
    lunr: ^2.3.9
    prism-react-renderer: ^1.2.1
    raw-loader: ^4.0.2
    react: ^17.0.1
    react-dom: ^17.0.1
    react-is: ^17.0.2
    styled-components: ^5.3.3
    typescript: ^4.5.4
  languageName: unknown
  linkType: soft

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.2, picomatch@npm:^2.2.3":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"pkg-up@npm:^3.1.0":
  version: 3.1.0
  resolution: "pkg-up@npm:3.1.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 5bac346b7c7c903613c057ae3ab722f320716199d753f4a7d053d38f2b5955460f3e6ab73b4762c62fd3e947f58e04f1343e92089e7bb6091c90877406fcd8c8
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.28":
  version: 1.0.28
  resolution: "portfinder@npm:1.0.28"
  dependencies:
    async: ^2.6.2
    debug: ^3.1.1
    mkdirp: ^0.5.5
  checksum: 91fef602f13f8f4c64385d0ad2a36cc9dc6be0b8d10a2628ee2c3c7b9917ab4fefb458815b82cea2abf4b785cd11c9b4e2d917ac6fa06f14b6fa880ca8f8928c
  languageName: node
  linkType: hard

"postcss-calc@npm:^8.2.0":
  version: 8.2.2
  resolution: "postcss-calc@npm:8.2.2"
  dependencies:
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.0.2
  peerDependencies:
    postcss: ^8.2.2
  checksum: aa60cbb01ae48ae8b708bc8c056e8ad6ddcc9f56af88c8e898c0b5d5d7c24f3448ec61698628d6c2e70893a657e99917ad77f18214c3d3bcab32477e855bccb4
  languageName: node
  linkType: hard

"postcss-colormin@npm:^5.2.3":
  version: 5.2.3
  resolution: "postcss-colormin@npm:5.2.3"
  dependencies:
    browserslist: ^4.16.6
    caniuse-api: ^3.0.0
    colord: ^2.9.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 5eae961d9e4801808c1cd0a7491bef49ffbb683d5c93f9009cbd3d6a168749ddace57db42bc3a1e54b8a534cd64513061724483bfefac0fed991319e7efc2ec6
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-convert-values@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 02a31f72b3365345db8aa1d83b084c96975d99a6494359378069431fd810e78ebf3bd96d03a598255daa8f6e2cd63722f119ddec9d24f66b6974b57819feb034
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-discard-comments@npm:5.0.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: c561952bbffa799cfc96216098d7ccc14b1dc776f0a8038c52eafe89fbec02701a234f35f7244aa06d58127103e7dd5f0bfd1db18a53c1438fef5c0a9b2dbddf
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-discard-duplicates@npm:5.0.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: becb68fd5ccd632fe51413a6ab4fd5c8aa3aae9d12947238014c2fb7816a2e0eb9a5454ee7207cac19f4a093c799be6053f13bf4048e97e20d88d5af4a0656bc
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-discard-empty@npm:5.0.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 2465ddabb18774c4996c18b8370498cf71597a23c45518ea75e7b73cd8f003b0be52ea9f27f28e24bba408d08ec5152e019cc595611bb097748993c1788d9f4f
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-discard-overridden@npm:5.0.2"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 3ac5b3382606bb7fc4c6fcd867e9f7b134be712bdbbfce378bcc7db72e86308db766f77af500ed5ac6b7c075dd29293e951b02438fdbd89d3943f70b581c8d02
  languageName: node
  linkType: hard

"postcss-discard-unused@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-discard-unused@npm:5.0.1"
  dependencies:
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: e8cd31ddca65bf5fba22c920cd8984ae8e7f504a1c1b91a2eb39851507b947677c366fd9d6a9e07b29293cb7ec887e1010aa499d2b5455819c5cd1fe304b065e
  languageName: node
  linkType: hard

"postcss-loader@npm:^6.1.1":
  version: 6.2.1
  resolution: "postcss-loader@npm:6.2.1"
  dependencies:
    cosmiconfig: ^7.0.0
    klona: ^2.0.5
    semver: ^7.3.5
  peerDependencies:
    postcss: ^7.0.0 || ^8.0.1
    webpack: ^5.0.0
  checksum: e40ae79c3e39df37014677a817b001bd115d8b10dedf53a07b97513d93b1533cd702d7a48831bdd77b9a9484b1ec84a5d4a723f80e83fb28682c75b5e65e8a90
  languageName: node
  linkType: hard

"postcss-merge-idents@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-merge-idents@npm:5.0.2"
  dependencies:
    cssnano-utils: ^3.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: fcee3a71d9738fe7b75400e013534f9f0fcabcf994672a07f398ab6726ee5e913409a9b149999d99e5428b7103f1dd4c88e59081b2b0f18d1cac65610e2a3de8
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^5.0.4":
  version: 5.0.4
  resolution: "postcss-merge-longhand@npm:5.0.4"
  dependencies:
    postcss-value-parser: ^4.1.0
    stylehacks: ^5.0.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6c5ff2ae0e9def05a59cbb432b5cbbdb968816b83c4e38fdf14fa596ef21e36442f61b53984d56dca6165d91e74eadc720270b2887a4a1ef5e25ee171b7d7ea0
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^5.0.4":
  version: 5.0.4
  resolution: "postcss-merge-rules@npm:5.0.4"
  dependencies:
    browserslist: ^4.16.6
    caniuse-api: ^3.0.0
    cssnano-utils: ^3.0.0
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 80189d794f2f1153f4191aacaf622b5f399a17dc6bb5baf4c80ed09ae14baf2285fbcfb686b16a0ca702fe6c2d443903260e753e86fb67dc01b66f4f13ce4ba8
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-minify-font-values@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: de54146819e838d6e813c685e1bce741caf1376033be229f91caac6a8b139936e8f795ea78fbfe9a29d5e108e437da47b3db690a370589518373d454d9c1c638
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^5.0.4":
  version: 5.0.4
  resolution: "postcss-minify-gradients@npm:5.0.4"
  dependencies:
    colord: ^2.9.1
    cssnano-utils: ^3.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: b102af3b435ff4280f2d256ba59a024dacbefc21e8d97ac1037a95bb18f29882f77a12381fd509efa339eacb2bfa3505e75bfa5e21a185841bd046044f032007
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^5.0.3":
  version: 5.0.3
  resolution: "postcss-minify-params@npm:5.0.3"
  dependencies:
    alphanum-sort: ^1.0.2
    browserslist: ^4.16.6
    cssnano-utils: ^3.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: e633067f02db428ef0300ad21b329ef04423843a69d24d69f3d253a7c6cd635f29e28158166936973d16d99d0da7204897224a345d102c2450da9690f09b024b
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-minify-selectors@npm:5.1.1"
  dependencies:
    alphanum-sort: ^1.0.2
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: a6073ae995bf989e9c1c4e79adcf443a1c70db04cb817bbe58025522bfb2d5765effa6047c445a028efc38d06885835fbf486fbd69332bd2823299a34a1dadaf
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-extract-imports@npm:3.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 4b65f2f1382d89c4bc3c0a1bdc5942f52f3cb19c110c57bd591ffab3a5fee03fcf831604168205b0c1b631a3dce2255c70b61aaae3ef39d69cd7eb450c2552d2
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-local-by-default@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: 6cf570badc7bc26c265e073f3ff9596b69bb954bc6ac9c5c1b8cba2995b80834226b60e0a3cbb87d5f399dbb52e6466bba8aa1d244f6218f99d834aec431a69d
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-scope@npm:3.0.0"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 330b9398dbd44c992c92b0dc612c0626135e2cc840fee41841eb61247a6cfed95af2bd6f67ead9dd9d0bb41f5b0367129d93c6e434fa3e9c58ade391d9a5a138
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-normalize-charset@npm:5.0.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: b74720bf0487809143a30e1965ff756698650abdd072f4fe81f0a32ce41e84c140f107b39ad0babf4d319aa620d1d4e01d1f89dc7c7b3f55fd3b27f243ee26e1
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-display-values@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 45d1975b98ca67bef1b27b247dff129fb3f2573471e416bcc528ee883a9425d51ba971dfc82c1e8e35389f047d4debe09be3a989aa250b1203c4e58158dcddc1
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-positions@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 29c1c48ac88c64ed34abdb86cb0d954860c4e4d0f971ec8e7ab26df28a6304ab6d5d1e974580b8cde97c800eef23ab4b03be4056563f8a6a01019608af0b2335
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-repeat-style@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d609d7b6b2ba7b42d72c1ffdbfc80af19f3864654f9f61d51a64b33f9a3428d84fccc85b53b9aa494ec404eb8de61b0288e4e9f820a008f4e00afea57c80fc98
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-string@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 79661e576ec058231f7415638a4c210c6716f7aed53ef6a4719883771ef6171ddc7a3720d838005d0b587f839ec54d18faeb08774b4014571e946f16574deef8
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-timing-functions@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: be6cd1ba6d1382669420ccf03f57302246585e7880e060045da528220f729543f89aedb8c2a31dddb2267f7afe8b3f8fbae4d9377b5a86cf6d723db1d64385dd
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-unicode@npm:5.0.2"
  dependencies:
    browserslist: ^4.16.6
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 452a1f2e559e4392cbf911d00d9ecae251233cf37bc81aa586be7ae93da3d1c14bd34bd89713777746845e5f4b2fbff0e0cc62ab24570041b29c62e8ece96b6c
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^5.0.4":
  version: 5.0.4
  resolution: "postcss-normalize-url@npm:5.0.4"
  dependencies:
    normalize-url: ^6.0.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 3c5a1d1723ab48811f1b888d065f8d9694d37f93fe3378a7672ec9c356a3ee96c84f1f7021c8c4a65f7caaa403f45df12b9b88de1fe66b0d1091d0f4fddf8233
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-normalize-whitespace@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: a8b602744dd0896928e1cec6b52230d5b652cb1dbf1e9b6e449529bd903b64879768b25ff2088c3bec2b67e96e13706bfec8e3d32a2f8700795f4a56f19e6f80
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^5.0.3":
  version: 5.0.3
  resolution: "postcss-ordered-values@npm:5.0.3"
  dependencies:
    cssnano-utils: ^3.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d441ecfbb09b5a298f8143e7a3d17b57e0278367245effe8213faba898b5126b34d1da8af57cbde859894df22e593beac943f64eaa13d377136b32622d90c30d
  languageName: node
  linkType: hard

"postcss-reduce-idents@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-reduce-idents@npm:5.0.1"
  dependencies:
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: eccefd99e975a41208eb14fc3626abf7228924b63ac75c675ef91881c4bba593e8f964b5ddd77b8a955253e336b9702543576d9beca8e394c461fcbf4f653bd4
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-reduce-initial@npm:5.0.2"
  dependencies:
    browserslist: ^4.16.6
    caniuse-api: ^3.0.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 324bdb897435a867f54e22d97fa3ea9f8aa71af68a1cbf8a3b918a41af83f7c810ea0726d7e59c93de0c997b0965fcb6c52e5a36755c34e558ccf7277f5bb8df
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-reduce-transforms@npm:5.0.2"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: eb7c758a108810d24c5ca55f2de2f9ccdaf8f76dbc39c363f124b2f8afffc5f93dae43eef2770c8704e71b34891ea69f67b9060abafc8ba3ca7cd10d01f63b06
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4, postcss-selector-parser@npm:^6.0.5":
  version: 6.0.8
  resolution: "postcss-selector-parser@npm:6.0.8"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: 550351c8d04216106e259f7c433652aa6742dd7ddbedf7afdc313526963bb170589a6fefd1bc1fe6268a2cf9f5073d4ecb09bc7b5b4bef49edf80634300500af
  languageName: node
  linkType: hard

"postcss-sort-media-queries@npm:^4.1.0":
  version: 4.2.1
  resolution: "postcss-sort-media-queries@npm:4.2.1"
  dependencies:
    sort-css-media-queries: 2.0.4
  peerDependencies:
    postcss: ^8.4.4
  checksum: 56a4363ce95a32daad74a40c22741f054de11210aaa1b5efc4c2dfb6eb9a651db6363479e0fe471e0f39d30ea95d2f97a89bd76f8394b7b42a3b36ee58f133e6
  languageName: node
  linkType: hard

"postcss-svgo@npm:^5.0.3":
  version: 5.0.3
  resolution: "postcss-svgo@npm:5.0.3"
  dependencies:
    postcss-value-parser: ^4.1.0
    svgo: ^2.7.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 7da0bfd6ecae300f1d82432d987ed3a4034a1502c4c458a0cf7284e172e8e86aa5098a89d9c23ee6b1360695c969f0f61ed776dd8098e26ee2a0b132ff1a7a5d
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^5.0.2":
  version: 5.0.2
  resolution: "postcss-unique-selectors@npm:5.0.2"
  dependencies:
    alphanum-sort: ^1.0.2
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: ad0f7a8a4f1ed958544c1ede62a1c4b0978e01627a6ef0642f7b044d0f9fdb331318a91f8312f418a773b0f2df06c50896cfaf7e5dd3d0142bd1e5ba75dc9eb7
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss-zindex@npm:^5.0.1":
  version: 5.0.1
  resolution: "postcss-zindex@npm:5.0.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: fcf3f32d490e1d5cf63d01416761f3bd81368549d256c0714f5cec9ee8a7e8eb3a950b44ea76c36ebba3678dbbbcd55f2eaadc1dd1949773146490862ac7779d
  languageName: node
  linkType: hard

"postcss@npm:^8.2.15, postcss@npm:^8.3.11, postcss@npm:^8.3.5, postcss@npm:^8.3.7":
  version: 8.4.5
  resolution: "postcss@npm:8.4.5"
  dependencies:
    nanoid: ^3.1.30
    picocolors: ^1.0.0
    source-map-js: ^1.0.1
  checksum: b78abdd89c10f7b48f4bdcd376104a19d6e9c7495ab521729bdb3df315af6c211360e9f06887ad3bc0ab0f61a04b91d68ea11462997c79cced58b9ccd66fac07
  languageName: node
  linkType: hard

"prepend-http@npm:^2.0.0":
  version: 2.0.0
  resolution: "prepend-http@npm:2.0.0"
  checksum: 7694a9525405447662c1ffd352fcb41b6410c705b739b6f4e3a3e21cf5fdede8377890088e8934436b8b17ba55365a615f153960f30877bf0d0392f9e93503ea
  languageName: node
  linkType: hard

"pretty-bytes@npm:^5.3.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 9c082500d1e93434b5b291bd651662936b8bd6204ec9fa17d563116a192d6d86b98f6d328526b4e8d783c07d5499e2614a807520249692da9ec81564b2f439cd
  languageName: node
  linkType: hard

"pretty-error@npm:^4.0.0":
  version: 4.0.0
  resolution: "pretty-error@npm:4.0.0"
  dependencies:
    lodash: ^4.17.20
    renderkid: ^3.0.0
  checksum: a5b9137365690104ded6947dca2e33360bf55e62a4acd91b1b0d7baa3970e43754c628cc9e16eafbdd4e8f8bcb260a5865475d4fc17c3106ff2d61db4e72cdf3
  languageName: node
  linkType: hard

"pretty-time@npm:^1.1.0":
  version: 1.1.0
  resolution: "pretty-time@npm:1.1.0"
  checksum: a319e7009aadbc6cfedbd8b66861327d3a0c68bd3e8794bf5b86f62b40b01b9479c5a70c76bb368ad454acce52a1216daee460cc825766e2442c04f3a84a02c9
  languageName: node
  linkType: hard

"prism-react-renderer@npm:^1.0.1, prism-react-renderer@npm:^1.2.1":
  version: 1.2.1
  resolution: "prism-react-renderer@npm:1.2.1"
  peerDependencies:
    react: ">=0.14.9"
  checksum: 66cab3d9f9f18ff5db222df74e1f326262ad093202f855ef2bd9e8001fef21b92f2242a2292caa77c36a517a7f379620417b421eaa320405deb6ecb27c18ee5e
  languageName: node
  linkType: hard

"prismjs@npm:^1.23.0":
  version: 1.26.0
  resolution: "prismjs@npm:1.26.0"
  checksum: 6de058930ce767d44d60322e625bbc255f5107c7bf82c441ade19a289358e01e35040cea0c17adf8f7b829f17aa48e2d84b40866b5b3f8c75a12c6b3f0ead231
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: ~2.0.3
  checksum: 475bb069130179fbd27ed2ab45f26d8862376a137a57314cf53310bdd85cc986a826fd585829be97ebc0aaf10e9d8e68be1bfe5a4a0364144b1f9eedfa940cf1
  languageName: node
  linkType: hard

"prompts@npm:^2.4.0, prompts@npm:^2.4.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"prop-types@npm:^15.5.8, prop-types@npm:^15.6.2, prop-types@npm:^15.7.2":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"property-information@npm:^5.0.0, property-information@npm:^5.3.0":
  version: 5.6.0
  resolution: "property-information@npm:5.6.0"
  dependencies:
    xtend: ^4.0.0
  checksum: fcf87c6542e59a8bbe31ca0b3255a4a63ac1059b01b04469680288998bcfa97f341ca989566adbb63975f4d85339030b82320c324a511532d390910d1c583893
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"punycode@npm:1.3.2":
  version: 1.3.2
  resolution: "punycode@npm:1.3.2"
  checksum: b8807fd594b1db33335692d1f03e8beeddde6fda7fbb4a2e32925d88d20a3aa4cd8dcc0c109ccaccbd2ba761c208dfaaada83007087ea8bfb0129c9ef1b99ed6
  languageName: node
  linkType: hard

"punycode@npm:^1.3.2":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: fa6e698cb53db45e4628559e557ddaf554103d2a96a1d62892c8f4032cd3bc8871796cae9eabc1bc700e2b6677611521ce5bb1d9a27700086039965d0cf34518
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.1.1
  resolution: "punycode@npm:2.1.1"
  checksum: 823bf443c6dd14f669984dea25757b37993f67e8d94698996064035edd43bed8a5a17a9f12e439c2b35df1078c6bec05a6c86e336209eb1061e8025c481168e8
  languageName: node
  linkType: hard

"pupa@npm:^2.1.1":
  version: 2.1.1
  resolution: "pupa@npm:2.1.1"
  dependencies:
    escape-goat: ^2.0.0
  checksum: 49529e50372ffdb0cccf0efa0f3b3cb0a2c77805d0d9cc2725bd2a0f6bb414631e61c93a38561b26be1259550b7bb6c2cb92315aa09c8bf93f3bdcb49f2b2fb7
  languageName: node
  linkType: hard

"pure-color@npm:^1.2.0":
  version: 1.3.0
  resolution: "pure-color@npm:1.3.0"
  checksum: 646d8bed6e6eab89affdd5e2c11f607a85b631a7fb03c061dfa658eb4dc4806881a15feed2ac5fd8c0bad8c00c632c640d5b1cb8b9a972e6e947393a1329371b
  languageName: node
  linkType: hard

"qs@npm:6.9.6":
  version: 6.9.6
  resolution: "qs@npm:6.9.6"
  checksum: cb6df402bb8a3dbefa4bd46eba0dfca427079baca923e6b8d28a03e6bfb16a5c1dcdb96e69388f9c5813ac8ff17bb8bbca22f2ecd31fe1e344a55cb531b5fabf
  languageName: node
  linkType: hard

"querystring@npm:0.2.0":
  version: 0.2.0
  resolution: "querystring@npm:0.2.0"
  checksum: 8258d6734f19be27e93f601758858c299bdebe71147909e367101ba459b95446fbe5b975bf9beb76390156a592b6f4ac3a68b6087cea165c259705b8b4e56a69
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"range-parser@npm:1.2.0":
  version: 1.2.0
  resolution: "range-parser@npm:1.2.0"
  checksum: bdf397f43fedc15c559d3be69c01dedf38444ca7a1610f5bf5955e3f3da6057a892f34691e7ebdd8c7e1698ce18ef6c4d4811f70e658dda3ff230ef741f8423a
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.4.2":
  version: 2.4.2
  resolution: "raw-body@npm:2.4.2"
  dependencies:
    bytes: 3.1.1
    http-errors: 1.8.1
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: c6f8d6a75c65c0a047f888cb29efc97f60fb36e950ba2cb31fefce694f98186e844a03367920faa7dc5bffaf33df08aee0b9dd935280e366439fa6492a5b163e
  languageName: node
  linkType: hard

"raw-loader@npm:^4.0.2":
  version: 4.0.2
  resolution: "raw-loader@npm:4.0.2"
  dependencies:
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 51cc1b0d0e8c37c4336b5318f3b2c9c51d6998ad6f56ea09612afcfefc9c1f596341309e934a744ae907177f28efc9f1654eacd62151e82853fcc6d37450e795
  languageName: node
  linkType: hard

"rc@npm:^1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-base16-styling@npm:^0.6.0":
  version: 0.6.0
  resolution: "react-base16-styling@npm:0.6.0"
  dependencies:
    base16: ^1.0.0
    lodash.curry: ^4.0.1
    lodash.flow: ^3.3.0
    pure-color: ^1.2.0
  checksum: 00a12dddafc8a9025cca933b0dcb65fca41c81fa176d1fc3a6a9d0242127042e2c0a604f4c724a3254dd2c6aeb5ef55095522ff22f5462e419641c1341a658e4
  languageName: node
  linkType: hard

"react-dev-utils@npm:12.0.0-next.47":
  version: 12.0.0-next.47
  resolution: "react-dev-utils@npm:12.0.0-next.47"
  dependencies:
    "@babel/code-frame": ^7.10.4
    address: ^1.1.2
    browserslist: ^4.16.5
    chalk: ^2.4.2
    cross-spawn: ^7.0.3
    detect-port-alt: ^1.1.6
    escape-string-regexp: ^2.0.0
    filesize: ^6.1.0
    find-up: ^4.1.0
    fork-ts-checker-webpack-plugin: ^6.0.5
    global-modules: ^2.0.0
    globby: ^11.0.1
    gzip-size: ^5.1.1
    immer: ^9.0.6
    is-root: ^2.1.0
    loader-utils: ^2.0.0
    open: ^7.0.2
    pkg-up: ^3.1.0
    prompts: ^2.4.0
    react-error-overlay: 7.0.0-next.54+1465357b
    recursive-readdir: ^2.2.2
    shell-quote: ^1.7.2
    strip-ansi: ^6.0.0
    text-table: ^0.2.0
  checksum: affdd7536ca5ab73720a8d54662286c2e946ca112abfe3701992b338cc8d3f39f7d48d702de10fa538c3eae7c328af6cd07dc4ad3f5864c8f3ea939e28f6c82d
  languageName: node
  linkType: hard

"react-dom@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-dom@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
    scheduler: ^0.20.2
  peerDependencies:
    react: 17.0.2
  checksum: 1c1eaa3bca7c7228d24b70932e3d7c99e70d1d04e13bb0843bbf321582bc25d7961d6b8a6978a58a598af2af496d1cedcfb1bf65f6b0960a0a8161cb8dab743c
  languageName: node
  linkType: hard

"react-error-overlay@npm:7.0.0-next.54+1465357b":
  version: 7.0.0-next.54
  resolution: "react-error-overlay@npm:7.0.0-next.54"
  checksum: 1d016364956e62b0585f4dd7d33510bb96ae968ac65ca71d481e344c625ff78a967a716495e77493e5c7bb9a8800f801af9b03959fbab6001d4d2822ed3a6789
  languageName: node
  linkType: hard

"react-error-overlay@npm:^6.0.9":
  version: 6.0.10
  resolution: "react-error-overlay@npm:6.0.10"
  checksum: e7384f086a0162eecac8e081fe3c79b32f4ac8690c56bde35ab6b6380d10e6c8375bbb689a450902b6615261fcf6c95ea016fc0b200934667089ca83536bc4a7
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.1.1":
  version: 3.2.0
  resolution: "react-fast-compare@npm:3.2.0"
  checksum: 8ef272c825ae329f61633ce4ce7f15aa5b84e5214d88bc0823880236e03e985a13195befa2c7a4eda7db3b017dc7985729152d88445823f652403cf36c2b86aa
  languageName: node
  linkType: hard

"react-helmet@npm:^6.1.0":
  version: 6.1.0
  resolution: "react-helmet@npm:6.1.0"
  dependencies:
    object-assign: ^4.1.1
    prop-types: ^15.7.2
    react-fast-compare: ^3.1.1
    react-side-effect: ^2.1.0
  peerDependencies:
    react: ">=16.3.0"
  checksum: a4998479dab7fc1c2799eddefb1870a9d881b5f71cfdf97979a9882e42f4bb50402d55335f308f461e735e01a06f46b16cc7b4e6bcb22c7a4a6f85a753c5c106
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.6.0, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^17.0.2":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 9d6d111d8990dc98bc5402c1266a808b0459b5d54830bbea24c12d908b536df7883f268a7868cfaedde3dd9d4e0d574db456f84d2e6df9c4526f99bb4b5344d8
  languageName: node
  linkType: hard

"react-json-view@npm:^1.21.3":
  version: 1.21.3
  resolution: "react-json-view@npm:1.21.3"
  dependencies:
    flux: ^4.0.1
    react-base16-styling: ^0.6.0
    react-lifecycles-compat: ^3.0.4
    react-textarea-autosize: ^8.3.2
  peerDependencies:
    react: ^17.0.0 || ^16.3.0 || ^15.5.4
    react-dom: ^17.0.0 || ^16.3.0 || ^15.5.4
  checksum: 5718bcd9210ad5b06eb9469cf8b9b44be9498845a7702e621343618e8251f26357e6e1c865532cf170db6165df1cb30202787e057309d8848c220bc600ec0d1a
  languageName: node
  linkType: hard

"react-lifecycles-compat@npm:^3.0.4":
  version: 3.0.4
  resolution: "react-lifecycles-compat@npm:3.0.4"
  checksum: a904b0fc0a8eeb15a148c9feb7bc17cec7ef96e71188280061fc340043fd6d8ee3ff233381f0e8f95c1cf926210b2c4a31f38182c8f35ac55057e453d6df204f
  languageName: node
  linkType: hard

"react-live@npm:2.2.3":
  version: 2.2.3
  resolution: "react-live@npm:2.2.3"
  dependencies:
    buble: 0.19.6
    core-js: ^2.4.1
    dom-iterator: ^1.0.0
    prism-react-renderer: ^1.0.1
    prop-types: ^15.5.8
    react-simple-code-editor: ^0.10.0
    unescape: ^1.0.1
  checksum: 2fbba62ac0647e18be7664a8e483d7bfede8c206e173e36042ff5dadbfd0359ae7d54d479dc1c25631cf531f99c03932749311ee6fd2a9d417ec18e5d63e6ec9
  languageName: node
  linkType: hard

"react-loadable-ssr-addon-v5-slorber@npm:^1.0.1":
  version: 1.0.1
  resolution: "react-loadable-ssr-addon-v5-slorber@npm:1.0.1"
  dependencies:
    "@babel/runtime": ^7.10.3
  peerDependencies:
    react-loadable: "*"
    webpack: ">=4.41.1 || 5.x"
  checksum: 1cf7ceb488d329a5be15f891dae16727fb7ade08ef57826addd21e2c3d485e2440259ef8be94f4d54e9afb4bcbd2fcc22c3c5bad92160c9c06ae6ba7b5562497
  languageName: node
  linkType: hard

"react-router-config@npm:^5.1.1":
  version: 5.1.1
  resolution: "react-router-config@npm:5.1.1"
  dependencies:
    "@babel/runtime": ^7.1.2
  peerDependencies:
    react: ">=15"
    react-router: ">=5"
  checksum: bde7ee79444454bf7c3737fd9c5c268021012c8cc37bc19116b2e7daa28c4231598c275816c7f32c16f9f974dc707b91de279291a5e39efce2e1b1569355b87a
  languageName: node
  linkType: hard

"react-router-dom@npm:^5.2.0":
  version: 5.3.0
  resolution: "react-router-dom@npm:5.3.0"
  dependencies:
    "@babel/runtime": ^7.12.13
    history: ^4.9.0
    loose-envify: ^1.3.1
    prop-types: ^15.6.2
    react-router: 5.2.1
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
  peerDependencies:
    react: ">=15"
  checksum: 47584fd629ecca52398d7888cab193b8a74057cc99a7ef44410c405d4082f618c3c0399db5325bc3524f9c511404086169570013b61a94dfa6acdfdc850d7a1f
  languageName: node
  linkType: hard

"react-router@npm:5.2.1, react-router@npm:^5.2.0":
  version: 5.2.1
  resolution: "react-router@npm:5.2.1"
  dependencies:
    "@babel/runtime": ^7.12.13
    history: ^4.9.0
    hoist-non-react-statics: ^3.1.0
    loose-envify: ^1.3.1
    mini-create-react-context: ^0.4.0
    path-to-regexp: ^1.7.0
    prop-types: ^15.6.2
    react-is: ^16.6.0
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
  peerDependencies:
    react: ">=15"
  checksum: 7daae084bf64531eb619cc5f4cc40ce5ae0a541b64f71d74ec71a38cbf6130ebdbb7cf38f157303fad5846deec259401f96c4d6c7386466dcc989719e01f9aaa
  languageName: node
  linkType: hard

"react-side-effect@npm:^2.1.0":
  version: 2.1.1
  resolution: "react-side-effect@npm:2.1.1"
  peerDependencies:
    react: ^16.3.0 || ^17.0.0
  checksum: 324511ea8f6669555e166b4af280cdf46034bf0e33c486711e3ce17f88f6f21fed17055098408be1347657d0cbcd614bca944cf9f8e4ecfa96a21d13893fe9fc
  languageName: node
  linkType: hard

"react-simple-code-editor@npm:^0.10.0":
  version: 0.10.0
  resolution: "react-simple-code-editor@npm:0.10.0"
  peerDependencies:
    react: ^16.0.0
    react-dom: ^16.0.0
  checksum: d9f448c5d1ba1b630ad6d9d775b5fa651ad1144eb5850fed3831b97d9da6da1daced37301c09946ff3a586f1f0f488f314cc81b744a1b60a343f30517c0767e9
  languageName: node
  linkType: hard

"react-textarea-autosize@npm:^8.3.2":
  version: 8.3.3
  resolution: "react-textarea-autosize@npm:8.3.3"
  dependencies:
    "@babel/runtime": ^7.10.2
    use-composed-ref: ^1.0.0
    use-latest: ^1.0.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0
  checksum: da3d0192825df3d9f27eef33e7eddf928359a7e3e2b01ae7f7f672ecf4e5c1f7a34f27bdde9ccc24e2e9fbe1d1b9dd2a39c7d47323c9bdf63e7b9bd05c325a71
  languageName: node
  linkType: hard

"react@npm:^17.0.1":
  version: 17.0.2
  resolution: "react@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: b254cc17ce3011788330f7bbf383ab653c6848902d7936a87b09d835d091e3f295f7e9dd1597c6daac5dc80f90e778c8230218ba8ad599f74adcc11e33b9d61b
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1":
  version: 2.3.7
  resolution: "readable-stream@npm:2.3.7"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: e4920cf7549a60f8aaf694d483a0e61b2a878b969d224f89b3bc788b8d920075132c4b55a7494ee944c7b6a9a0eada28a7f6220d80b0312ece70bbf08eeca755
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.1.1, readable-stream@npm:^3.6.0":
  version: 3.6.0
  resolution: "readable-stream@npm:3.6.0"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: d4ea81502d3799439bb955a3a5d1d808592cf3133350ed352aeaa499647858b27b1c4013984900238b0873ec8d0d8defce72469fb7a83e61d53f5ad61cb80dc8
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"reading-time@npm:^1.5.0":
  version: 1.5.0
  resolution: "reading-time@npm:1.5.0"
  checksum: e27bc5a70ba0f4ac337896b18531b914d38f4bee67cbad48029d0c11dd0a7a847b2a6bba895ab7ce2ad3e7ecb86912bdc477d8fa2d48405a3deda964be54d09b
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: ^1.1.6
  checksum: fe76bf9c21875ac16e235defedd7cbd34f333c02a92546142b7911a0f7c7059d2e16f441fe6fb9ae203f459c05a31b2bcf26202896d89e390eda7514d5d2702b
  languageName: node
  linkType: hard

"recursive-readdir@npm:^2.2.2":
  version: 2.2.2
  resolution: "recursive-readdir@npm:2.2.2"
  dependencies:
    minimatch: 3.0.4
  checksum: a6b22994d76458443d4a27f5fd7147ac63ad31bba972666a291d511d4d819ee40ff71ba7524c14f6a565b8cfaf7f48b318f971804b913cf538d58f04e25d1fee
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^9.0.0":
  version: 9.0.0
  resolution: "regenerate-unicode-properties@npm:9.0.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: 62df21c274259a68c6fa1373e5ddb4d6f6374ad72c08dd488b7802880bc1c3b6de716303ec56c9f793a73d01815e9d81f03a8fbe3f32bc0f7fdf8d70d4841b64
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.4":
  version: 0.13.9
  resolution: "regenerator-runtime@npm:0.13.9"
  checksum: 65ed455fe5afd799e2897baf691ca21c2772e1a969d19bb0c4695757c2d96249eb74ee3553ea34a91062b2a676beedf630b4c1551cc6299afb937be1426ec55e
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.14.2":
  version: 0.14.5
  resolution: "regenerator-transform@npm:0.14.5"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: a467a3b652b4ec26ff964e9c5f1817523a73fc44cb928b8d21ff11aebeac5d10a84d297fe02cea9f282bcec81a0b0d562237da69ef0f40a0160b30a4fa98bc94
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.2.0, regexp.prototype.flags@npm:^1.3.1":
  version: 1.3.1
  resolution: "regexp.prototype.flags@npm:1.3.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 343595db5a6bbbb3bfbda881f9c74832cfa9fc0039e64a43843f6bb9158b78b921055266510800ed69d4997638890b17a46d55fd9f32961f53ae56ac3ec4dd05
  languageName: node
  linkType: hard

"regexpu-core@npm:^4.2.0, regexpu-core@npm:^4.5.4, regexpu-core@npm:^4.7.1":
  version: 4.8.0
  resolution: "regexpu-core@npm:4.8.0"
  dependencies:
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^9.0.0
    regjsgen: ^0.5.2
    regjsparser: ^0.7.0
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.0.0
  checksum: df92e3e6482409f0a0de162ca1b4e17897e9b0b0687caead6804f04e9b89847e47abbfd0bfc62f52a0b833acf764ea5bdb7b707bb088034824a675ee95d31dec
  languageName: node
  linkType: hard

"registry-auth-token@npm:^4.0.0":
  version: 4.2.1
  resolution: "registry-auth-token@npm:4.2.1"
  dependencies:
    rc: ^1.2.8
  checksum: aa72060b573a50607cfd2dee16d0e51e13ca58b6a80442e74545325dc24d2c38896e6bad229bdcc1fc9759fa81b4066be8693d4d6f45927318e7c793a93e9cd0
  languageName: node
  linkType: hard

"registry-url@npm:^5.0.0":
  version: 5.1.0
  resolution: "registry-url@npm:5.1.0"
  dependencies:
    rc: ^1.2.8
  checksum: bcea86c84a0dbb66467b53187fadebfea79017cddfb4a45cf27530d7275e49082fe9f44301976eb0164c438e395684bcf3dae4819b36ff9d1640d8cc60c73df9
  languageName: node
  linkType: hard

"regjsgen@npm:^0.5.2":
  version: 0.5.2
  resolution: "regjsgen@npm:0.5.2"
  checksum: 87c83d8488affae2493a823904de1a29a1867a07433c5e1142ad749b5606c5589b305fe35bfcc0972cf5a3b0d66b1f7999009e541be39a5d42c6041c59e2fb52
  languageName: node
  linkType: hard

"regjsparser@npm:^0.7.0":
  version: 0.7.0
  resolution: "regjsparser@npm:0.7.0"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: fefff9adcab47650817d2c492aac774f11a44b824a4a814e466ebc76313e03e79c50d2babde7e04888296f6ec0fd094e3eeeafa8122c60184de92cdb30636a57
  languageName: node
  linkType: hard

"rehype-parse@npm:^6.0.2":
  version: 6.0.2
  resolution: "rehype-parse@npm:6.0.2"
  dependencies:
    hast-util-from-parse5: ^5.0.0
    parse5: ^5.0.0
    xtend: ^4.0.0
  checksum: f9afca7a8038a402d45d2f6eab31b2ce09100c195007c0bf9340b32e31585c6898f1cf0f4e088c08c5e2adade0fbb59e490ec6291e16751b12bd24d7c1e48ba9
  languageName: node
  linkType: hard

"rehype-parse@npm:^7.0.1":
  version: 7.0.1
  resolution: "rehype-parse@npm:7.0.1"
  dependencies:
    hast-util-from-parse5: ^6.0.0
    parse5: ^6.0.0
  checksum: c3c914aa9281853290eff6b09e0bed6843934e788b957e25219e91f0bf244a183d2f5e042c7d21543276571f9b49a6bae90f4640b8f885f2773392ffa57baf4b
  languageName: node
  linkType: hard

"relateurl@npm:^0.2.7":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 5891e792eae1dfc3da91c6fda76d6c3de0333a60aa5ad848982ebb6dccaa06e86385fb1235a1582c680a3d445d31be01c6bfc0804ebbcab5aaf53fa856fde6b6
  languageName: node
  linkType: hard

"remark-admonitions@npm:^1.2.1":
  version: 1.2.1
  resolution: "remark-admonitions@npm:1.2.1"
  dependencies:
    rehype-parse: ^6.0.2
    unified: ^8.4.2
    unist-util-visit: ^2.0.1
  checksum: c80fbc08b57c0054d7b414c8a0a205dee24d53ca9344a055acc3e1d0770d4045ffd7bec244d2316cf4c0cc27cf1a52be29332e7d9595000dbf3276a0b2f04b86
  languageName: node
  linkType: hard

"remark-emoji@npm:^2.1.0":
  version: 2.2.0
  resolution: "remark-emoji@npm:2.2.0"
  dependencies:
    emoticon: ^3.2.0
    node-emoji: ^1.10.0
    unist-util-visit: ^2.0.3
  checksum: 638d4be72eb4110a447f389d4b8c454921f188c0acabf1b6579f3ddaa301ee91010173d6eebd975ea622ae3de7ed4531c0315a4ffd4f9653d80c599ef9ec21a8
  languageName: node
  linkType: hard

"remark-footnotes@npm:2.0.0":
  version: 2.0.0
  resolution: "remark-footnotes@npm:2.0.0"
  checksum: f2f87ffd6fe25892373c7164d6584a7cb03ab0ea4f186af493a73df519e24b72998a556e7f16cb996f18426cdb80556b95ff252769e252cf3ccba0fd2ca20621
  languageName: node
  linkType: hard

"remark-mdx-remove-exports@npm:^1.6.22":
  version: 1.6.22
  resolution: "remark-mdx-remove-exports@npm:1.6.22"
  dependencies:
    unist-util-remove: 2.0.0
  checksum: 4ab3e34167982d4e022a867cb5b5447d8cbc0a123f51f8b60839e50873239d135b4ff69a40a21c07bba08729ca144eebee6f09203e9681707e027036f57e0b9e
  languageName: node
  linkType: hard

"remark-mdx-remove-imports@npm:^1.6.22":
  version: 1.6.22
  resolution: "remark-mdx-remove-imports@npm:1.6.22"
  dependencies:
    unist-util-remove: 2.0.0
  checksum: ed33293fb18c9d6b93ddc1d59b07c022c8c1b1dec21c65f81e05a08bec025c7347402b589d28339eb902157438ede86d14a817ce7b927650997341e655a56dc6
  languageName: node
  linkType: hard

"remark-mdx@npm:1.6.22":
  version: 1.6.22
  resolution: "remark-mdx@npm:1.6.22"
  dependencies:
    "@babel/core": 7.12.9
    "@babel/helper-plugin-utils": 7.10.4
    "@babel/plugin-proposal-object-rest-spread": 7.12.1
    "@babel/plugin-syntax-jsx": 7.12.1
    "@mdx-js/util": 1.6.22
    is-alphabetical: 1.0.4
    remark-parse: 8.0.3
    unified: 9.2.0
  checksum: 45e62f8a821c37261f94448d54f295de1c5c393f762ff96cd4d4b730715037fafeb6c89ef94adf6a10a09edfa72104afe1431b93b5ae5e40ce2a7677e133c3d9
  languageName: node
  linkType: hard

"remark-parse@npm:8.0.3":
  version: 8.0.3
  resolution: "remark-parse@npm:8.0.3"
  dependencies:
    ccount: ^1.0.0
    collapse-white-space: ^1.0.2
    is-alphabetical: ^1.0.0
    is-decimal: ^1.0.0
    is-whitespace-character: ^1.0.0
    is-word-character: ^1.0.0
    markdown-escapes: ^1.0.0
    parse-entities: ^2.0.0
    repeat-string: ^1.5.4
    state-toggle: ^1.0.0
    trim: 0.0.1
    trim-trailing-lines: ^1.0.0
    unherit: ^1.0.4
    unist-util-remove-position: ^2.0.0
    vfile-location: ^3.0.0
    xtend: ^4.0.1
  checksum: 2dfea250e7606ddfc9e223b9f41e0b115c5c701be4bd35181beaadd46ee59816bc00aadc6085a420f8df00b991ada73b590ea7fd34ace14557de4a0a41805be5
  languageName: node
  linkType: hard

"remark-squeeze-paragraphs@npm:4.0.0":
  version: 4.0.0
  resolution: "remark-squeeze-paragraphs@npm:4.0.0"
  dependencies:
    mdast-squeeze-paragraphs: ^4.0.0
  checksum: 2071eb74d0ecfefb152c4932690a9fd950c3f9f798a676f1378a16db051da68fb20bf288688cc153ba5019dded35408ff45a31dfe9686eaa7a9f1df9edbb6c81
  languageName: node
  linkType: hard

"renderkid@npm:^3.0.0":
  version: 3.0.0
  resolution: "renderkid@npm:3.0.0"
  dependencies:
    css-select: ^4.1.3
    dom-converter: ^0.2.0
    htmlparser2: ^6.1.0
    lodash: ^4.17.21
    strip-ansi: ^6.0.1
  checksum: 77162b62d6f33ab81f337c39efce0439ff0d1f6d441e29c35183151f83041c7850774fb904da163d6c844264d440d10557714e6daa0b19e4561a5cd4ef305d41
  languageName: node
  linkType: hard

"repeat-string@npm:^1.0.0, repeat-string@npm:^1.5.4":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"require-like@npm:>= 0.1.1":
  version: 0.1.2
  resolution: "require-like@npm:0.1.2"
  checksum: edb8331f05fd807381a75b76f6cca9f0ce8acaa2e910b7e116541799aa970bfbc64fde5fd6adb3a6917dba346f8386ebbddb81614c24e8dad1b4290c7af9535e
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pathname@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-pathname@npm:3.0.0"
  checksum: 6147241ba42c423dbe83cb067a2b4af4f60908c3af57e1ea567729cc71416c089737fe2a73e9e79e7a60f00f66c91e4b45ad0d37cd4be2d43fec44963ef14368
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6, resolve@npm:^1.14.2, resolve@npm:^1.19.0, resolve@npm:^1.3.2":
  version: 1.21.0
  resolution: "resolve@npm:1.21.0"
  dependencies:
    is-core-module: ^2.8.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: d7d9092a5c04a048bea16c7e5a2eb605ac3e8363a0cc5644de1fde17d5028e8d5f4343aab1d99bd327b98e91a66ea83e242718150c64dfedcb96e5e7aad6c4f5
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.6#~builtin<compat/resolve>, resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.19.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.3.2#~builtin<compat/resolve>":
  version: 1.21.0
  resolution: "resolve@patch:resolve@npm%3A1.21.0#~builtin<compat/resolve>::version=1.21.0&hash=07638b"
  dependencies:
    is-core-module: ^2.8.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a0a4d1f7409e73190f31f901f8a619960bb3bd4ae38ba3a54c7ea7e1c87758d28a73256bb8d6a35996a903d1bf14f53883f0dcac6c571c063cb8162d813ad26e
  languageName: node
  linkType: hard

"responselike@npm:^1.0.2":
  version: 1.0.2
  resolution: "responselike@npm:1.0.2"
  dependencies:
    lowercase-keys: ^1.0.0
  checksum: 2e9e70f1dcca3da621a80ce71f2f9a9cad12c047145c6ece20df22f0743f051cf7c73505e109814915f23f9e34fb0d358e22827723ee3d56b623533cab8eafcd
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 47c4d5be674f7c13eee4cfe927345023972197dbbdfba5d3af7e461d13b44de1bfd663bfc80d2f601f8ef3fc8164c16dd99655a221921954a65d044a2fc1233b
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rollup-plugin-terser@npm:^7.0.0":
  version: 7.0.2
  resolution: "rollup-plugin-terser@npm:7.0.2"
  dependencies:
    "@babel/code-frame": ^7.10.4
    jest-worker: ^26.2.1
    serialize-javascript: ^4.0.0
    terser: ^5.0.0
  peerDependencies:
    rollup: ^2.0.0
  checksum: af84bb7a7a894cd00852b6486528dfb8653cf94df4c126f95f389a346f401d054b08c46bee519a2ab6a22b33804d1d6ac6d8c90b1b2bf8fffb097eed73fc3c72
  languageName: node
  linkType: hard

"rollup@npm:^2.43.1":
  version: 2.63.0
  resolution: "rollup@npm:2.63.0"
  dependencies:
    fsevents: ~2.3.2
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 23db16ea9d222ad5ae9620ba51d4f45c834927038c1e43d87f7dd3d240aa54422e51c2660437479af4b771e13f9529df236a3d43a3b9f4229bf241347d5f2c8f
  languageName: node
  linkType: hard

"rtl-detect@npm:^1.0.4":
  version: 1.0.4
  resolution: "rtl-detect@npm:1.0.4"
  checksum: d562535baa0db62f57f0a1d4676297bff72fd6b94e88f0f0900d5c3e810ab512c5c4cadffd3e05fbe8d9c74310c919afa3ea8c1001c244e5555e8eef12d02d6f
  languageName: node
  linkType: hard

"rtlcss@npm:^3.3.0":
  version: 3.5.0
  resolution: "rtlcss@npm:3.5.0"
  dependencies:
    find-up: ^5.0.0
    picocolors: ^1.0.0
    postcss: ^8.3.11
    strip-json-comments: ^3.1.1
  bin:
    rtlcss: bin/rtlcss.js
  checksum: a3763cad2cb58ce1b950de155097c3c294e7aefc8bf328b58d0cc8d5efb88bf800865edc158a78ace6d1f7f99fea6fd66fb4a354d859b172dadd3dab3e0027b3
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.1.0":
  version: 7.5.2
  resolution: "rxjs@npm:7.5.2"
  dependencies:
    tslib: ^2.1.0
  checksum: daf1fe7289de500b25d822fd96cde3c138c7902e8bf0e6aa12a3e70847a5cabeeb4d677f10e19387e1db44b12c5b1be0ff5c79b8cd63ed6ce891d765e566cf4d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2, safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sax@npm:^1.2.4":
  version: 1.2.4
  resolution: "sax@npm:1.2.4"
  checksum: d3df7d32b897a2c2f28e941f732c71ba90e27c24f62ee918bd4d9a8cfb3553f2f81e5493c7f0be94a11c1911b643a9108f231dd6f60df3fa9586b5d2e3e9e1fe
  languageName: node
  linkType: hard

"scheduler@npm:^0.20.2":
  version: 0.20.2
  resolution: "scheduler@npm:0.20.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: c4b35cf967c8f0d3e65753252d0f260271f81a81e427241295c5a7b783abf4ea9e905f22f815ab66676f5313be0a25f47be582254db8f9241b259213e999b8fc
  languageName: node
  linkType: hard

"schema-utils@npm:2.7.0":
  version: 2.7.0
  resolution: "schema-utils@npm:2.7.0"
  dependencies:
    "@types/json-schema": ^7.0.4
    ajv: ^6.12.2
    ajv-keywords: ^3.4.1
  checksum: 8889325b0ee1ae6a8f5d6aaa855c71e136ebbb7fd731b01a9d3ec8225dcb245f644c47c50104db4c741983b528cdff8558570021257d4d397ec6aaecd9172a8e
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0, schema-utils@npm:^3.1.0, schema-utils@npm:^3.1.1":
  version: 3.1.1
  resolution: "schema-utils@npm:3.1.1"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: fb73f3d759d43ba033c877628fe9751620a26879f6301d3dbeeb48cf2a65baec5cdf99da65d1bf3b4ff5444b2e59cbe4f81c2456b5e0d2ba7d7fd4aed5da29ce
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0":
  version: 4.0.0
  resolution: "schema-utils@npm:4.0.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.8.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.0.0
  checksum: c843e92fdd1a5c145dbb6ffdae33e501867f9703afac67bdf35a685e49f85b1dcc10ea250033175a64bd9d31f0555bc6785b8359da0c90bcea30cf6dfbb55a8f
  languageName: node
  linkType: hard

"section-matter@npm:^1.0.0":
  version: 1.0.0
  resolution: "section-matter@npm:1.0.0"
  dependencies:
    extend-shallow: ^2.0.1
    kind-of: ^6.0.0
  checksum: 3cc4131705493b2955729b075dcf562359bba66183debb0332752dc9cad35616f6da7a23e42b6cab45cd2e4bb5cda113e9e84c8f05aee77adb6b0289a0229101
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: d7e5fcc695a4804209d232a1b18624a5134be334d4e1114b0721f7a5e72bd73da483dcf41528c1af4f4f4892ad7cfd6a1e55c8ffb83f9c9fe723b738db609dbb
  languageName: node
  linkType: hard

"selfsigned@npm:^2.0.0":
  version: 2.0.0
  resolution: "selfsigned@npm:2.0.0"
  dependencies:
    node-forge: ^1.2.0
  checksum: 43fca39a5aded2a8e97c1756af74c049a9dde12d47d302820f7d507d25c2ad7da4b04bc439a36620d63b4c0149bcf34ae7a729f978bf3b1bf48859c36ae34cee
  languageName: node
  linkType: hard

"semver-diff@npm:^3.1.1":
  version: 3.1.1
  resolution: "semver-diff@npm:3.1.1"
  dependencies:
    semver: ^6.3.0
  checksum: 8bbe5a5d7add2d5e51b72314a9215cd294d71f41cdc2bf6bd59ee76411f3610b576172896f1d191d0d7294cb9f2f847438d2ee158adacc0c224dca79052812fe
  languageName: node
  linkType: hard

"semver@npm:7.0.0":
  version: 7.0.0
  resolution: "semver@npm:7.0.0"
  bin:
    semver: bin/semver.js
  checksum: 272c11bf8d083274ef79fe40a81c55c184dff84dd58e3c325299d0927ba48cece1f020793d138382b85f89bab5002a35a5ba59a3a68a7eebbb597eb733838778
  languageName: node
  linkType: hard

"semver@npm:^5.4.1":
  version: 5.7.1
  resolution: "semver@npm:5.7.1"
  bin:
    semver: ./bin/semver
  checksum: 57fd0acfd0bac382ee87cd52cd0aaa5af086a7dc8d60379dfe65fea491fb2489b6016400813930ecd61fd0952dae75c115287a1b16c234b1550887117744dfaf
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.1.1, semver@npm:^6.1.2, semver@npm:^6.2.0, semver@npm:^6.3.0":
  version: 6.3.0
  resolution: "semver@npm:6.3.0"
  bin:
    semver: ./bin/semver.js
  checksum: 1b26ecf6db9e8292dd90df4e781d91875c0dcc1b1909e70f5d12959a23c7eebb8f01ea581c00783bbee72ceeaad9505797c381756326073850dc36ed284b21b9
  languageName: node
  linkType: hard

"semver@npm:^7.3.2, semver@npm:^7.3.4, semver@npm:^7.3.5":
  version: 7.3.5
  resolution: "semver@npm:7.3.5"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 5eafe6102bea2a7439897c1856362e31cc348ccf96efd455c8b5bc2c61e6f7e7b8250dc26b8828c1d76a56f818a7ee907a36ae9fb37a599d3d24609207001d60
  languageName: node
  linkType: hard

"send@npm:0.17.2":
  version: 0.17.2
  resolution: "send@npm:0.17.2"
  dependencies:
    debug: 2.6.9
    depd: ~1.1.2
    destroy: ~1.0.4
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 1.8.1
    mime: 1.6.0
    ms: 2.1.3
    on-finished: ~2.3.0
    range-parser: ~1.2.1
    statuses: ~1.5.0
  checksum: c28f36deb4ccba9b8d6e6a1e472b8e7c40a1f51575bdf8f67303568cc9e71131faa3adc36fdb72611616ccad1584358bbe4c3ebf419e663ecc5de868ad3d3f03
  languageName: node
  linkType: hard

"serialize-javascript@npm:^4.0.0":
  version: 4.0.0
  resolution: "serialize-javascript@npm:4.0.0"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3273b3394b951671fcf388726e9577021870dfbf85e742a1183fb2e91273e6101bdccea81ff230724f6659a7ee4cef924b0ff9baca32b79d9384ec37caf07302
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.0":
  version: 6.0.0
  resolution: "serialize-javascript@npm:6.0.0"
  dependencies:
    randombytes: ^2.1.0
  checksum: 56f90b562a1bdc92e55afb3e657c6397c01a902c588c0fe3d4c490efdcc97dcd2a3074ba12df9e94630f33a5ce5b76a74784a7041294628a6f4306e0ec84bf93
  languageName: node
  linkType: hard

"serve-handler@npm:^6.1.3":
  version: 6.1.3
  resolution: "serve-handler@npm:6.1.3"
  dependencies:
    bytes: 3.0.0
    content-disposition: 0.5.2
    fast-url-parser: 1.1.3
    mime-types: 2.1.18
    minimatch: 3.0.4
    path-is-inside: 1.0.2
    path-to-regexp: 2.2.1
    range-parser: 1.2.0
  checksum: 384c1bc10add07a554207f918acaa75af47fcfd8fb89e070faa3468ab45ec5bbc9f976e62d659b6b63404edcf5c54efb7e0a48f3f55946eec83b62b283b9837e
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: ~1.3.4
    batch: 0.6.1
    debug: 2.6.9
    escape-html: ~1.0.3
    http-errors: ~1.6.2
    mime-types: ~2.1.17
    parseurl: ~1.3.2
  checksum: e2647ce13379485b98a53ba2ea3fbad4d44b57540d00663b02b976e426e6194d62ac465c0d862cb7057f65e0de8ab8a684aa095427a4b8612412eca0d300d22f
  languageName: node
  linkType: hard

"serve-static@npm:1.14.2":
  version: 1.14.2
  resolution: "serve-static@npm:1.14.2"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.17.2
  checksum: d97f3183b1dfcd8ce9c0e37e18e87fd31147ed6c8ee0b2c3a089d795e44ee851ca5061db01574f806d54f4e4b70bc694d9ca64578653514e04a28cbc97a1de05
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 27cb44304d6c9e1a23bc6c706af4acaae1a7aa1054d4ec13c05f01a99fd4887109a83a8042b67ad90dbfcd100d43efc171ee036eb080667172079213242ca36e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.7.2":
  version: 1.7.3
  resolution: "shell-quote@npm:1.7.3"
  checksum: aca58e73a3a5d933d02e0bdddedc53ee14f7c2ec264f97ac915b9d4482d077a38e422aa664631d60a672cd3cdb4054eb2e6c0303f54882453dacb6483e482d34
  languageName: node
  linkType: hard

"shelljs@npm:^0.8.4":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: ^7.0.0
    interpret: ^1.0.0
    rechoir: ^0.6.2
  bin:
    shjs: bin/shjs
  checksum: 7babc46f732a98f4c054ec1f048b55b9149b98aa2da32f6cf9844c434b43c6251efebd6eec120937bd0999e13811ebd45efe17410edb3ca938f82f9381302748
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3":
  version: 3.0.6
  resolution: "signal-exit@npm:3.0.6"
  checksum: b819ac81ba757af559dad0804233ae31bf6f054591cd8a671e9cbcf09f21c72ec3076fe87d1e04861f5b33b47d63f0694b568de99c99cd733ee2060515beb6d5
  languageName: node
  linkType: hard

"sirv@npm:^1.0.7":
  version: 1.0.19
  resolution: "sirv@npm:1.0.19"
  dependencies:
    "@polka/url": ^1.0.0-next.20
    mrmime: ^1.0.0
    totalist: ^1.0.0
  checksum: c943cfc61baf85f05f125451796212ec35d4377af4da90ae8ec1fa23e6d7b0b4d9c74a8fbf65af83c94e669e88a09dc6451ba99154235eead4393c10dda5b07c
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"sitemap@npm:^7.0.0":
  version: 7.1.0
  resolution: "sitemap@npm:7.1.0"
  dependencies:
    "@types/node": ^17.0.5
    "@types/sax": ^1.2.1
    arg: ^5.0.0
    sax: ^1.2.4
  bin:
    sitemap: dist/cli.js
  checksum: 2dd4deb78c79f43f18a7bc11600d7bc1c10215ac5276bdb183440df739ba8d5f4607f716fe90eef73eeb05a0eab2e0834f28d9d4159ae4a7c51c3f9ecf0e4a0c
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.1.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.21":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: ^0.11.3
    uuid: ^8.3.2
    websocket-driver: ^0.7.4
  checksum: 355309b48d2c4e9755349daa29cea1c0d9ee23e49b983841c6bf7a20276b00d3c02343f9f33f26d2ee8b261a5a02961b52a25c8da88b2538c5b68d3071b4934c
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^6.0.0":
  version: 6.1.1
  resolution: "socks-proxy-agent@npm:6.1.1"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.1
    socks: ^2.6.1
  checksum: 9a8a4f791bba0060315cf7291ca6f9db37d6fc280fd0860d73d8887d3efe4c22e823aa25a8d5375f6079279f8dc91b50c075345179bf832bfe3c7c26d3582e3c
  languageName: node
  linkType: hard

"socks@npm:^2.6.1":
  version: 2.6.1
  resolution: "socks@npm:2.6.1"
  dependencies:
    ip: ^1.1.5
    smart-buffer: ^4.1.0
  checksum: 2ca9d616e424f645838ebaabb04f85d94ea999e0f8393dc07f86c435af22ed88cb83958feeabd1bb7bc537c635ed47454255635502c6808a6df61af1f41af750
  languageName: node
  linkType: hard

"sort-css-media-queries@npm:2.0.4":
  version: 2.0.4
  resolution: "sort-css-media-queries@npm:2.0.4"
  checksum: 610661adf57c9cdb8da5de80cdc4753b4ebec6cd14081e7aca95384bd62a4dea7677c5018cdcb111352b2ae6f3c2ac0591f24381c74096dd3972c87e489dc5b7
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 806efc6f75e7cd31e4815e7a3aaf75a45c704871ea4075cb2eb49882c6fca28998f44fc5ac91adb6de03b2882ee6fb02f951fdc85e6a22b338c32bfe19557938
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1":
  version: 1.0.1
  resolution: "source-map-js@npm:1.0.1"
  checksum: 22606113d62bbd468712b0cb0c46e9a8629de7eb081049c62a04d977a211abafd7d61455617f8b78daba0b6c0c7e7c88f8c6b5aaeacffac0a6676ecf5caac5ce
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: 64c5c2c77aff815a6e61a4120c309ae4cac01298d9bcbb3deb1b46a4dd4c46d4a1eaeda79ec9f684766ae80e8dc86367b89326ce9dd2b89947bd9291fc1ac08c
  languageName: node
  linkType: hard

"source-map@npm:^0.5.0":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:^0.8.0-beta.0":
  version: 0.8.0-beta.0
  resolution: "source-map@npm:0.8.0-beta.0"
  dependencies:
    whatwg-url: ^7.0.0
  checksum: e94169be6461ab0ac0913313ad1719a14c60d402bd22b0ad96f4a6cffd79130d91ab5df0a5336a326b04d2df131c1409f563c9dc0d21a6ca6239a44b6c8dbd92
  languageName: node
  linkType: hard

"source-map@npm:~0.7.2":
  version: 0.7.3
  resolution: "source-map@npm:0.7.3"
  checksum: cd24efb3b8fa69b64bf28e3c1b1a500de77e84260c5b7f2b873f88284df17974157cc88d386ee9b6d081f08fdd8242f3fc05c953685a6ad81aad94c7393dedea
  languageName: node
  linkType: hard

"sourcemap-codec@npm:^1.4.4":
  version: 1.4.8
  resolution: "sourcemap-codec@npm:1.4.8"
  checksum: b57981c05611afef31605732b598ccf65124a9fcb03b833532659ac4d29ac0f7bfacbc0d6c5a28a03e84c7510e7e556d758d0bb57786e214660016fb94279316
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^1.0.0":
  version: 1.1.5
  resolution: "space-separated-tokens@npm:1.1.5"
  checksum: 8ef68f1cfa8ccad316b7f8d0df0919d0f1f6d32101e8faeee34ea3a923ce8509c1ad562f57388585ee4951e92d27afa211ed0a077d3d5995b5ba9180331be708
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: ^4.1.0
    detect-node: ^2.0.4
    hpack.js: ^2.1.6
    obuf: ^1.1.2
    readable-stream: ^3.0.6
    wbuf: ^1.7.3
  checksum: 0fcaad3b836fb1ec0bdd39fa7008b9a7a84a553f12be6b736a2512613b323207ffc924b9551cef0378f7233c85916cff1118652e03a730bdb97c0e042243d56c
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: ^4.1.0
    handle-thing: ^2.0.0
    http-deceiver: ^1.2.7
    select-hose: ^2.0.0
    spdy-transport: ^3.0.0
  checksum: 2c739d0ff6f56ad36d2d754d0261d5ec358457bea7cbf77b1b05b0c6464f2ce65b85f196305f50b7bd9120723eb94bae9933466f28e67e5cd8cde4e27f1d75f8
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^8.0.0, ssri@npm:^8.0.1":
  version: 8.0.1
  resolution: "ssri@npm:8.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: bc447f5af814fa9713aa201ec2522208ae0f4d8f3bda7a1f445a797c7b929a02720436ff7c478fb5edc4045adb02b1b88d2341b436a80798734e2494f1067b36
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: 2ff482bb100285d16dd75cd8f7c60ab652570e8952c0bfa91828a2b5f646a0ff533f14596ea4eabd48bb7f4aeea408dce8f8515812b975d958a4cc4fa6b9dfeb
  languageName: node
  linkType: hard

"state-toggle@npm:^1.0.0":
  version: 1.0.3
  resolution: "state-toggle@npm:1.0.3"
  checksum: 17398af928413e8d8b866cf0c81fd1b1348bb7d65d8983126ff6ff2317a80d6ee023484fba0c54d8169f5aa544f125434a650ae3a71eddc935cae307d4692b4f
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2, statuses@npm:>= 1.5.0 < 2, statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"std-env@npm:^3.0.1":
  version: 3.0.1
  resolution: "std-env@npm:3.0.1"
  checksum: 253f6175554a2339fdd745a09c30747d2d8e99ce32d1f5fb56652c023ae6c4778a9058821e1d3a06b6cc64a7b98ca65434ed6b589f05456c7c8e97308df0de81
  languageName: node
  linkType: hard

"string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.2, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.6":
  version: 4.0.6
  resolution: "string.prototype.matchall@npm:4.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.1
    get-intrinsic: ^1.1.1
    has-symbols: ^1.0.2
    internal-slot: ^1.0.3
    regexp.prototype.flags: ^1.3.1
    side-channel: ^1.0.4
  checksum: 07aca53ddd8a096a8bd0560eb8574386c6b3887a6a06b40a98abd42c94dadeed3296261fca22fec59a1ed970d199bdeb450fcb6a7390193588d9c6b5f48fe842
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.4":
  version: 1.0.4
  resolution: "string.prototype.trimend@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 17e5aa45c3983f582693161f972c1c1fa4bbbdf22e70e582b00c91b6575f01680dc34e83005b98e31abe4d5d29e0b21fcc24690239c106c7b2315aade6a898ac
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.4":
  version: 1.0.4
  resolution: "string.prototype.trimstart@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 3fb06818d3cccac5fa3f5f9873d984794ca0e9f6616fae6fcc745885d9efed4e17fe15f832515d9af5e16c279857fdbffdfc489ca4ed577811b017721b30302f
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"stringify-object@npm:^3.3.0":
  version: 3.3.0
  resolution: "stringify-object@npm:3.3.0"
  dependencies:
    get-own-enumerable-property-symbols: ^3.0.0
    is-obj: ^1.0.1
    is-regexp: ^1.0.0
  checksum: 6827a3f35975cfa8572e8cd3ed4f7b262def260af18655c6fde549334acdac49ddba69f3c861ea5a6e9c5a4990fe4ae870b9c0e6c31019430504c94a83b7a154
  languageName: node
  linkType: hard

"strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.0":
  version: 7.0.1
  resolution: "strip-ansi@npm:7.0.1"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 257f78fa433520e7f9897722731d78599cb3fce29ff26a20a5e12ba4957463b50a01136f37c43707f4951817a75e90820174853d6ccc240997adc5df8f966039
  languageName: node
  linkType: hard

"strip-bom-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-bom-string@npm:1.0.0"
  checksum: 5635a3656d8512a2c194d6c8d5dee7ef0dde6802f7be9413b91e201981ad4132506656d9cf14137f019fd50f0269390d91c7f6a2601b1bee039a4859cfce4934
  languageName: node
  linkType: hard

"strip-comments@npm:^2.0.1":
  version: 2.0.1
  resolution: "strip-comments@npm:2.0.1"
  checksum: 36cd122e1c27b5be69df87e1687770a62fe183bdee9f3ff5cf85d30bbc98280afc012581f2fd50c7ad077c90f656f272560c9d2e520d28604b8b7ea3bc87d6f9
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"style-to-object@npm:0.3.0, style-to-object@npm:^0.3.0":
  version: 0.3.0
  resolution: "style-to-object@npm:0.3.0"
  dependencies:
    inline-style-parser: 0.1.1
  checksum: 4d7084015207f2a606dfc10c29cb5ba569f2fe8005551df7396110dd694d6ff650f2debafa95bd5d147dfb4ca50f57868e2a7f91bf5d11ef734fe7ccbd7abf59
  languageName: node
  linkType: hard

"styled-components@npm:^5.3.3":
  version: 5.3.3
  resolution: "styled-components@npm:5.3.3"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    "@babel/traverse": ^7.4.5
    "@emotion/is-prop-valid": ^0.8.8
    "@emotion/stylis": ^0.8.4
    "@emotion/unitless": ^0.7.4
    babel-plugin-styled-components: ">= 1.12.0"
    css-to-react-native: ^3.0.0
    hoist-non-react-statics: ^3.0.0
    shallowequal: ^1.1.0
    supports-color: ^5.5.0
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
    react-is: ">= 16.8.0"
  checksum: a104341068fc39fa2c73950a34970d832dc7a511fc52b3df12f34e6746031f1f128f53b4d540bf39d9f0da043cf0d91517faf874d2c87de5e385f5c2e7620436
  languageName: node
  linkType: hard

"stylehacks@npm:^5.0.1":
  version: 5.0.1
  resolution: "stylehacks@npm:5.0.1"
  dependencies:
    browserslist: ^4.16.0
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.2.15
  checksum: 777dbed3987e04f713b9d74e08f66ab4c23c76cabb07c666c0ae9a06e58e8961063e17b5c7b9c23421b75e9caa9fb78084688e509624e57b19c92c174fbd964d
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0, supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.2":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: b3de6653048212f2ae7afe4a423e04a76ec6d2d06e1bf7eacc618a7c5f7df7faa5105561c57b94579ec831fbbdbf5f190ba56a9205ff39ed13eabdf8ab086ddf
  languageName: node
  linkType: hard

"svgo@npm:^2.5.0, svgo@npm:^2.7.0":
  version: 2.8.0
  resolution: "svgo@npm:2.8.0"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^4.1.3
    css-tree: ^1.1.3
    csso: ^4.2.0
    picocolors: ^1.0.0
    stable: ^0.1.8
  bin:
    svgo: bin/svgo
  checksum: b92f71a8541468ffd0b81b8cdb36b1e242eea320bf3c1a9b2c8809945853e9d8c80c19744267eb91cabf06ae9d5fff3592d677df85a31be4ed59ff78534fa420
  languageName: node
  linkType: hard

"tapable@npm:^1.0.0":
  version: 1.1.3
  resolution: "tapable@npm:1.1.3"
  checksum: 53ff4e7c3900051c38cc4faab428ebfd7e6ad0841af5a7ac6d5f3045c5b50e88497bfa8295b4b3fbcadd94993c9e358868b78b9fb249a76cb8b018ac8dccafd7
  languageName: node
  linkType: hard

"tapable@npm:^2.0.0, tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^6.0.2, tar@npm:^6.1.2":
  version: 6.1.11
  resolution: "tar@npm:6.1.11"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^3.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: a04c07bb9e2d8f46776517d4618f2406fb977a74d914ad98b264fc3db0fe8224da5bec11e5f8902c5b9bcb8ace22d95fbe3c7b36b8593b7dfc8391a25898f32f
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: cc4f0404bf8d6ae1a166e0e64f3f409b423f4d1274d8c02814a59a5529f07db6cd070a749664141b992b2c1af337fa9bb451a460a43bb9bcddc49f235d3115aa
  languageName: node
  linkType: hard

"tempy@npm:^0.6.0":
  version: 0.6.0
  resolution: "tempy@npm:0.6.0"
  dependencies:
    is-stream: ^2.0.0
    temp-dir: ^2.0.0
    type-fest: ^0.16.0
    unique-string: ^2.0.0
  checksum: dd09c8b6615e4b785ea878e9a18b17ac0bfe5dccf5a0e205ebd274bb356356aff3f5c90a6c917077d51c75efb7648b113a78b0492e2ffc81a7c9912eb872ac52
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.1.3, terser-webpack-plugin@npm:^5.2.4":
  version: 5.3.0
  resolution: "terser-webpack-plugin@npm:5.3.0"
  dependencies:
    jest-worker: ^27.4.1
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.0
    source-map: ^0.6.1
    terser: ^5.7.2
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: f6735b8bb2604e8ca8b78d21f610fb2488866db72bb38e8d7c32aab97ea81fa0a19cabed074a431ff3dd9510d6efd505fc6930cdd8c1d3faa71c1bf7da4c7469
  languageName: node
  linkType: hard

"terser@npm:^5.0.0, terser@npm:^5.10.0, terser@npm:^5.7.2":
  version: 5.10.0
  resolution: "terser@npm:5.10.0"
  dependencies:
    commander: ^2.20.0
    source-map: ~0.7.2
    source-map-support: ~0.5.20
  peerDependencies:
    acorn: ^8.5.0
  peerDependenciesMeta:
    acorn:
      optional: true
  bin:
    terser: bin/terser
  checksum: 1080faeb6d5cd155bb39d9cc41d20a590eafc9869560d5285f255f6858604dcd135311e344188a106f87fedb12d096ad3799cfc2e65acd470b85d468b1c7bd4c
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 993096c472b6b8f30e29dc777a8d17720e4cab448375041f20c0cb802a09a7fb2217f2a3e8cdc11851faa71c957e2db309357367fc9d7af3cb7a4d00f4b66034
  languageName: node
  linkType: hard

"timsort@npm:^0.3.0":
  version: 0.3.0
  resolution: "timsort@npm:0.3.0"
  checksum: 1a66cb897dacabd7dd7c91b7e2301498ca9e224de2edb9e42d19f5b17c4b6dc62a8d4cbc64f28be82aaf1541cb5a78ab49aa818f42a2989ebe049a64af731e2a
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.0.2":
  version: 1.2.0
  resolution: "tiny-invariant@npm:1.2.0"
  checksum: e09a718a7c4a499ba592cdac61f015d87427a0867ca07f50c11fd9b623f90cdba18937b515d4a5e4f43dac92370498d7bdaee0d0e7a377a61095e02c4a92eade
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.0, tiny-warning@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: da62c4acac565902f0624b123eed6dd3509bc9a8d30c06e017104bedcf5d35810da8ff72864400ad19c5c7806fc0a8323c68baf3e326af7cb7d969f846100d71
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-readable-stream@npm:^1.0.0":
  version: 1.0.0
  resolution: "to-readable-stream@npm:1.0.0"
  checksum: 2bd7778490b6214a2c40276065dd88949f4cf7037ce3964c76838b8cb212893aeb9cceaaf4352a4c486e3336214c350270f3263e1ce7a0c38863a715a4d9aeb5
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"to-vfile@npm:^6.1.0":
  version: 6.1.0
  resolution: "to-vfile@npm:6.1.0"
  dependencies:
    is-buffer: ^2.0.0
    vfile: ^4.0.0
  checksum: 7331aecca00d591bb904277e7ba65b9a12275a4ab035b1dd2cf21ec22f27cca4d0ee9802e73485e9c4bd8a4ca219c740a3ff724413327fb51c784466c8be18fc
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"totalist@npm:^1.0.0":
  version: 1.1.0
  resolution: "totalist@npm:1.1.0"
  checksum: dfab80c7104a1d170adc8c18782d6c04b7df08352dec452191208c66395f7ef2af7537ddfa2cf1decbdcfab1a47afbbf0dec6543ea191da98c1c6e1599f86adc
  languageName: node
  linkType: hard

"tr46@npm:^1.0.1":
  version: 1.0.1
  resolution: "tr46@npm:1.0.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 96d4ed46bc161db75dbf9247a236ea0bfcaf5758baae6749e92afab0bc5a09cb59af21788ede7e55080f2bf02dce3e4a8f2a484cc45164e29f4b5e68f7cbcc1a
  languageName: node
  linkType: hard

"trim-trailing-lines@npm:^1.0.0":
  version: 1.1.4
  resolution: "trim-trailing-lines@npm:1.1.4"
  checksum: 5d39d21c0d4b258667012fcd784f73129e148ea1c213b1851d8904f80499fc91df6710c94c7dd49a486a32da2b9cb86020dda79f285a9a2586cfa622f80490c2
  languageName: node
  linkType: hard

"trim@npm:0.0.1":
  version: 0.0.1
  resolution: "trim@npm:0.0.1"
  checksum: 2b4646dff99a222e8e1526edd4e3a43bbd925af0b8e837c340455d250157e7deefaa4da49bb891ab841e5c27b1afc5e9e32d4b57afb875d2dfcabf4e319b8f7f
  languageName: node
  linkType: hard

"trough@npm:^1.0.0":
  version: 1.0.5
  resolution: "trough@npm:1.0.5"
  checksum: d6c8564903ed00e5258bab92134b020724dbbe83148dc72e4bf6306c03ed8843efa1bcc773fa62410dd89161ecb067432dd5916501793508a9506cacbc408e25
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.3.1":
  version: 2.3.1
  resolution: "tslib@npm:2.3.1"
  checksum: de17a98d4614481f7fcb5cd53ffc1aaf8654313be0291e1bfaee4b4bb31a20494b7d218ff2e15017883e8ea9626599b3b0e0229c18383ba9dce89da2adf15cb9
  languageName: node
  linkType: hard

"type-fest@npm:^0.16.0":
  version: 0.16.0
  resolution: "type-fest@npm:0.16.0"
  checksum: 1a4102c06dc109db00418c753062e206cab65befd469d000ece4452ee649bf2a9cf57686d96fb42326bc9d918d9a194d4452897b486dcc41989e5c99e4e87094
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: ^1.0.0
  checksum: 99c11aaa8f45189fcfba6b8a4825fd684a321caa9bd7a76a27cf0c7732c174d198b99f449c52c3818107430b5f41c0ccbbfb75cb2ee3ca4a9451710986d61a60
  languageName: node
  linkType: hard

"typescript@npm:^4.5.4":
  version: 4.5.4
  resolution: "typescript@npm:4.5.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 59f3243f9cd6fe3161e6150ff6bf795fc843b4234a655dbd938a310515e0d61afd1ac942799e7415e4334255e41c2c49b7dd5d9fd38a17acd25a6779ca7e0961
  languageName: node
  linkType: hard

"typescript@patch:typescript@^4.5.4#~builtin<compat/typescript>":
  version: 4.5.4
  resolution: "typescript@patch:typescript@npm%3A4.5.4#~builtin<compat/typescript>::version=4.5.4&hash=493e53"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 2e488dde7d2c4a2fa2e79cf2470600f8ce81bc0563c276b72df8ff412d74456ae532ba824650ae936ce207440c79720ddcfaa25e3cb4477572b8534fa4e34d49
  languageName: node
  linkType: hard

"ua-parser-js@npm:^0.7.30":
  version: 0.7.31
  resolution: "ua-parser-js@npm:0.7.31"
  checksum: e2f8324a83d1715601576af85b2b6c03890699aaa7272950fc77ea925c70c5e4f75060ae147dc92124e49f7f0e3d6dd2b0a91e7f40d267e92df8894be967ba8b
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.1":
  version: 1.0.1
  resolution: "unbox-primitive@npm:1.0.1"
  dependencies:
    function-bind: ^1.1.1
    has-bigints: ^1.0.1
    has-symbols: ^1.0.2
    which-boxed-primitive: ^1.0.2
  checksum: 89d950e18fb45672bc6b3c961f1e72c07beb9640c7ceed847b571ba6f7d2af570ae1a2584cfee268b9d9ea1e3293f7e33e0bc29eaeb9f8e8a0bab057ff9e6bba
  languageName: node
  linkType: hard

"unescape@npm:^1.0.1":
  version: 1.0.1
  resolution: "unescape@npm:1.0.1"
  dependencies:
    extend-shallow: ^2.0.1
  checksum: 0d89b0f55e08a2843e635f1ccf8472a35b367c41d9a8014dd7de5cc3af710a6e988a950b86b6229e143147ade21772f2d72054bc846f4972eb448df472b856ec
  languageName: node
  linkType: hard

"unherit@npm:^1.0.4":
  version: 1.1.3
  resolution: "unherit@npm:1.1.3"
  dependencies:
    inherits: ^2.0.0
    xtend: ^4.0.0
  checksum: fd7922f84fc0bfb7c4df6d1f5a50b5b94a0218e3cda98a54dbbd209226ddd4072d742d3df44d0e295ab08d5ccfd304a1e193dfe31a86d2a91b7cb9fdac093194
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.0.0"
  checksum: 8fe6a09d9085a625cabcead5d95bdbc1a2d5d481712856092ce0347231e81a60b93a68f1b69e82b3076a07e415a72c708044efa2aa40ae23e2e7b5c99ed4a9ea
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.0.0"
  checksum: dda4d39128cbbede2ac60fbb85493d979ec65913b8a486bf7cb7a375a2346fa48cbf9dc6f1ae23376e7e8e684c2b411434891e151e865a661b40a85407db51d0
  languageName: node
  linkType: hard

"unified@npm:9.2.0":
  version: 9.2.0
  resolution: "unified@npm:9.2.0"
  dependencies:
    bail: ^1.0.0
    extend: ^3.0.0
    is-buffer: ^2.0.0
    is-plain-obj: ^2.0.0
    trough: ^1.0.0
    vfile: ^4.0.0
  checksum: 0cac4ae119893fbd49d309b4db48595e4d4e9f0a2dc1dde4d0074059f9a46012a2905f37c1346715e583f30c970bc8078db8462675411d39ff5036ae18b4fb8a
  languageName: node
  linkType: hard

"unified@npm:^8.4.2":
  version: 8.4.2
  resolution: "unified@npm:8.4.2"
  dependencies:
    bail: ^1.0.0
    extend: ^3.0.0
    is-plain-obj: ^2.0.0
    trough: ^1.0.0
    vfile: ^4.0.0
  checksum: c2af7662d6375b14721df305786b15ba3228cd39c37da748bff00ed08ababd12ce52568f475347f270b1dea72fb0b9608563574a55c29e4f73f8be7ce0a01b4a
  languageName: node
  linkType: hard

"unified@npm:^9.0.0":
  version: 9.2.2
  resolution: "unified@npm:9.2.2"
  dependencies:
    bail: ^1.0.0
    extend: ^3.0.0
    is-buffer: ^2.0.0
    is-plain-obj: ^2.0.0
    trough: ^1.0.0
    vfile: ^4.0.0
  checksum: 7c24461be7de4145939739ce50d18227c5fbdf9b3bc5a29dabb1ce26dd3e8bd4a1c385865f6f825f3b49230953ee8b591f23beab3bb3643e3e9dc37aa8a089d5
  languageName: node
  linkType: hard

"unique-filename@npm:^1.1.1":
  version: 1.1.1
  resolution: "unique-filename@npm:1.1.1"
  dependencies:
    unique-slug: ^2.0.0
  checksum: cf4998c9228cc7647ba7814e255dec51be43673903897b1786eff2ac2d670f54d4d733357eb08dea969aa5e6875d0e1bd391d668fbdb5a179744e7c7551a6f80
  languageName: node
  linkType: hard

"unique-slug@npm:^2.0.0":
  version: 2.0.2
  resolution: "unique-slug@npm:2.0.2"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 5b6876a645da08d505dedb970d1571f6cebdf87044cb6b740c8dbb24f0d6e1dc8bdbf46825fd09f994d7cf50760e6f6e063cfa197d51c5902c00a861702eb75a
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: ^2.0.0
  checksum: ef68f639136bcfe040cf7e3cd7a8dff076a665288122855148a6f7134092e6ed33bf83a7f3a9185e46c98dddc445a0da6ac25612afa1a7c38b8b654d6c02498e
  languageName: node
  linkType: hard

"unist-builder@npm:2.0.3, unist-builder@npm:^2.0.0":
  version: 2.0.3
  resolution: "unist-builder@npm:2.0.3"
  checksum: e946fdf77dbfc320feaece137ce4959ae2da6614abd1623bd39512dc741a9d5f313eb2ba79f8887d941365dccddec7fef4e953827475e392bf49b45336f597f6
  languageName: node
  linkType: hard

"unist-util-find-after@npm:^3.0.0":
  version: 3.0.0
  resolution: "unist-util-find-after@npm:3.0.0"
  dependencies:
    unist-util-is: ^4.0.0
  checksum: daa9a28f6cdf533a72ce7ec4864dbe0f11f0fd3efd337b54c08a8a9a47cdc8d10a299cd984d7f512a57e97af012df052210a51aab7c9afd6b1e24da3b2d0a714
  languageName: node
  linkType: hard

"unist-util-generated@npm:^1.0.0":
  version: 1.1.6
  resolution: "unist-util-generated@npm:1.1.6"
  checksum: 86239ff88a08800d52198f2f0e15911f05bab2dad17cef95550f7c2728f15ebb0344694fcc3101d05762d88adaf86cb85aa7a3300fedabd0b6d7d00b41cdcb7f
  languageName: node
  linkType: hard

"unist-util-is@npm:^4.0.0, unist-util-is@npm:^4.0.2":
  version: 4.1.0
  resolution: "unist-util-is@npm:4.1.0"
  checksum: 726484cd2adc9be75a939aeedd48720f88294899c2e4a3143da413ae593f2b28037570730d5cf5fd910ff41f3bc1501e3d636b6814c478d71126581ef695f7ea
  languageName: node
  linkType: hard

"unist-util-position@npm:^3.0.0":
  version: 3.1.0
  resolution: "unist-util-position@npm:3.1.0"
  checksum: 10b3952e32a1ffabbecad41c3946237f7059f5bb6436796da05531a285f50b97e4f37cfc2f7164676d041063f40fe1ad92fbb8ca38d3ae8747328ebe738d738f
  languageName: node
  linkType: hard

"unist-util-remove-position@npm:^2.0.0":
  version: 2.0.1
  resolution: "unist-util-remove-position@npm:2.0.1"
  dependencies:
    unist-util-visit: ^2.0.0
  checksum: 4149294969f1a78a367b5d03eb0a138aa8320a39e1b15686647a2bec5945af3df27f2936a1e9752ecbb4a82dc23bd86f7e5a0ee048e5eeaedc2deb9237872795
  languageName: node
  linkType: hard

"unist-util-remove@npm:2.0.0":
  version: 2.0.0
  resolution: "unist-util-remove@npm:2.0.0"
  dependencies:
    unist-util-is: ^4.0.0
  checksum: 0e0bddf890e5de2eed6cd2dc5178f70ff5ff497e60877f9e4242b87418d24f272a684c3fb200c810f032e6bc9847bf0b40e3aefb3e8fde1059f1b34d3991adc9
  languageName: node
  linkType: hard

"unist-util-remove@npm:^2.0.0":
  version: 2.1.0
  resolution: "unist-util-remove@npm:2.1.0"
  dependencies:
    unist-util-is: ^4.0.0
  checksum: 99e54f3ea0523f8cf957579a6e84e5b58427bffab929cc7f6aa5119581f929db683dd4691ea5483df0c272f486dda9dbd04f4ab74dca6cae1f3ebe8e4261a4d9
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^2.0.0":
  version: 2.0.3
  resolution: "unist-util-stringify-position@npm:2.0.3"
  dependencies:
    "@types/unist": ^2.0.2
  checksum: f755cadc959f9074fe999578a1a242761296705a7fe87f333a37c00044de74ab4b184b3812989a57d4cd12211f0b14ad397b327c3a594c7af84361b1c25a7f09
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^3.0.0":
  version: 3.1.1
  resolution: "unist-util-visit-parents@npm:3.1.1"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-is: ^4.0.0
  checksum: 1170e397dff88fab01e76d5154981666eb0291019d2462cff7a2961a3e76d3533b42eaa16b5b7e2d41ad42a5ea7d112301458283d255993e660511387bf67bc3
  languageName: node
  linkType: hard

"unist-util-visit@npm:2.0.3, unist-util-visit@npm:^2.0.0, unist-util-visit@npm:^2.0.1, unist-util-visit@npm:^2.0.2, unist-util-visit@npm:^2.0.3":
  version: 2.0.3
  resolution: "unist-util-visit@npm:2.0.3"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-is: ^4.0.0
    unist-util-visit-parents: ^3.0.0
  checksum: 1fe19d500e212128f96d8c3cfa3312846e586b797748a1fd195fe6479f06bc90a6f6904deb08eefc00dd58e83a1c8a32fb8677252d2273ad7a5e624525b69b8f
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.0
  resolution: "universalify@npm:2.0.0"
  checksum: 2406a4edf4a8830aa6813278bab1f953a8e40f2f63a37873ffa9a3bc8f9745d06cc8e88f3572cb899b7e509013f7f6fcc3e37e8a6d914167a5381d8440518c44
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"upath@npm:^1.2.0":
  version: 1.2.0
  resolution: "upath@npm:1.2.0"
  checksum: 4c05c094797cb733193a0784774dbea5b1889d502fc9f0572164177e185e4a59ba7099bf0b0adf945b232e2ac60363f9bf18aac9b2206fb99cbef971a8455445
  languageName: node
  linkType: hard

"update-notifier@npm:^5.1.0":
  version: 5.1.0
  resolution: "update-notifier@npm:5.1.0"
  dependencies:
    boxen: ^5.0.0
    chalk: ^4.1.0
    configstore: ^5.0.1
    has-yarn: ^2.1.0
    import-lazy: ^2.1.0
    is-ci: ^2.0.0
    is-installed-globally: ^0.4.0
    is-npm: ^5.0.0
    is-yarn-global: ^0.3.0
    latest-version: ^5.1.0
    pupa: ^2.1.1
    semver: ^7.3.4
    semver-diff: ^3.1.1
    xdg-basedir: ^4.0.0
  checksum: 461e5e5b002419296d3868ee2abe0f9ab3e1846d9db642936d0c46f838872ec56069eddfe662c45ce1af0a8d6d5026353728de2e0a95ab2e3546a22ea077caf1
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-loader@npm:^4.1.1":
  version: 4.1.1
  resolution: "url-loader@npm:4.1.1"
  dependencies:
    loader-utils: ^2.0.0
    mime-types: ^2.1.27
    schema-utils: ^3.0.0
  peerDependencies:
    file-loader: "*"
    webpack: ^4.0.0 || ^5.0.0
  peerDependenciesMeta:
    file-loader:
      optional: true
  checksum: c1122a992c6cff70a7e56dfc2b7474534d48eb40b2cc75467cde0c6972e7597faf8e43acb4f45f93c2473645dfd803bcbc20960b57544dd1e4c96e77f72ba6fd
  languageName: node
  linkType: hard

"url-parse-lax@npm:^3.0.0":
  version: 3.0.0
  resolution: "url-parse-lax@npm:3.0.0"
  dependencies:
    prepend-http: ^2.0.0
  checksum: 1040e357750451173132228036aff1fd04abbd43eac1fb3e4fca7495a078bcb8d33cb765fe71ad7e473d9c94d98fd67adca63bd2716c815a2da066198dd37217
  languageName: node
  linkType: hard

"url@npm:^0.11.0":
  version: 0.11.0
  resolution: "url@npm:0.11.0"
  dependencies:
    punycode: 1.3.2
    querystring: 0.2.0
  checksum: 50d100d3dd2d98b9fe3ada48cadb0b08aa6be6d3ac64112b867b56b19be4bfcba03c2a9a0d7922bfd7ac17d4834e88537749fe182430dfd9b68e520175900d90
  languageName: node
  linkType: hard

"use-composed-ref@npm:^1.0.0":
  version: 1.2.1
  resolution: "use-composed-ref@npm:1.2.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0
  checksum: 27238fef7184bfdd4be24901188d3f5fe641536a7aee0f7b435166d3c0bc958b7d84c4c512c4737422a623b07ca7ee95f5eca2fa3ea52722fcc66bc367bd32bc
  languageName: node
  linkType: hard

"use-isomorphic-layout-effect@npm:^1.0.0":
  version: 1.1.1
  resolution: "use-isomorphic-layout-effect@npm:1.1.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: fd9061817d4945af37fd79866b1fe96a09cafe873169a66ec699140b609c64db6c60512d94ec3ca90967837026ea6e6d003901c557693708aeee11d392418a9e
  languageName: node
  linkType: hard

"use-latest@npm:^1.0.0":
  version: 1.2.0
  resolution: "use-latest@npm:1.2.0"
  dependencies:
    use-isomorphic-layout-effect: ^1.0.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: f0cb3a49119e14ed46db8a946b1aa17b838b8834c8a652bde314877ede6057c55b50654a97ee802597a5839c070180195e58ea3a756b7c33db7f540642f0ddea
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 97ffd3bd2bb80c773429d3fb8396469115cd190dded1e733f190d8b602bd0a1bcd6216b7ce3c4395ee3c79e3c879c19d268dbaae3093564cb169ad1212d436f4
  languageName: node
  linkType: hard

"utility-types@npm:^3.10.0":
  version: 3.10.0
  resolution: "utility-types@npm:3.10.0"
  checksum: 8f274415c6196ab62883b8bd98c9d2f8829b58016e4269aaa1ebd84184ac5dda7dc2ca45800c0d5e0e0650966ba063bf9a412aaeaea6850ca4440a391283d5c8
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"value-equal@npm:^1.0.1":
  version: 1.0.1
  resolution: "value-equal@npm:1.0.1"
  checksum: bb7ae1facc76b5cf8071aeb6c13d284d023fdb370478d10a5d64508e0e6e53bb459c4bbe34258df29d82e6f561f874f0105eba38de0e61fe9edd0bdce07a77a2
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vfile-location@npm:^3.0.0, vfile-location@npm:^3.2.0":
  version: 3.2.0
  resolution: "vfile-location@npm:3.2.0"
  checksum: 9bb3df6d0be31b5dd2d8da0170c27b7045c64493a8ba7b6ff7af8596c524fc8896924b8dd85ae12d201eead2709217a0fbc44927b7264f4bbf0aa8027a78be9c
  languageName: node
  linkType: hard

"vfile-message@npm:^2.0.0":
  version: 2.0.4
  resolution: "vfile-message@npm:2.0.4"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-stringify-position: ^2.0.0
  checksum: 1bade499790f46ca5aba04bdce07a1e37c2636a8872e05cf32c26becc912826710b7eb063d30c5754fdfaeedc8a7658e78df10b3bc535c844890ec8a184f5643
  languageName: node
  linkType: hard

"vfile@npm:^4.0.0":
  version: 4.2.1
  resolution: "vfile@npm:4.2.1"
  dependencies:
    "@types/unist": ^2.0.0
    is-buffer: ^2.0.0
    unist-util-stringify-position: ^2.0.0
    vfile-message: ^2.0.0
  checksum: ee5726e10d170472cde778fc22e0f7499caa096eb85babea5d0ce0941455b721037ee1c9e6ae506ca2803250acd313d0f464328ead0b55cfe7cb6315f1b462d6
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 67ab6dd35c787eaa02c0ff1a869dd07a230db08722fb6014adaaf432634808ddb070765f70958b47997e438c331790cfcf20902411b0d6453f1a2a5923522f55
  languageName: node
  linkType: hard

"wait-on@npm:^6.0.0":
  version: 6.0.0
  resolution: "wait-on@npm:6.0.0"
  dependencies:
    axios: ^0.21.1
    joi: ^17.4.0
    lodash: ^4.17.21
    minimist: ^1.2.5
    rxjs: ^7.1.0
  bin:
    wait-on: bin/wait-on
  checksum: 6ae7bd2a933715c3b2f1c49f033d97c576b2c6a0257420d4c83964d2846c3967bfce33bc9af9a1a631ef38dfa6185be03cef57d2867c8c30c523278f964ac9e3
  languageName: node
  linkType: hard

"watchpack@npm:^2.3.1":
  version: 2.3.1
  resolution: "watchpack@npm:2.3.1"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 70a34f92842d94b5d842980f866d568d7a467de667c96ae5759c759f46587e49265863171f4650bdbafc5f3870a28f2b4453e9e847098ec4b718b38926d47d22
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: ^1.0.0
  checksum: 2abc306c96930b757972a1c4650eb6b25b5d99f24088714957f88629e137db569368c5de0e57986c89ea70db2f1df9bba11a87cb6d0c8694b6f53a0159fab3bf
  languageName: node
  linkType: hard

"web-namespaces@npm:^1.0.0, web-namespaces@npm:^1.1.2":
  version: 1.1.4
  resolution: "web-namespaces@npm:1.1.4"
  checksum: 5149842ccbfbc56fe4f8758957b3f8c8616a281874a5bb84aa1b305e4436a9bad853d21c629a7b8f174902449e1489c7a6c724fccf60965077c5636bd8aed42b
  languageName: node
  linkType: hard

"webidl-conversions@npm:^4.0.2":
  version: 4.0.2
  resolution: "webidl-conversions@npm:4.0.2"
  checksum: c93d8dfe908a0140a4ae9c0ebc87a33805b416a33ee638a605b551523eec94a9632165e54632f6d57a39c5f948c4bab10e0e066525e9a4b87a79f0d04fbca374
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^4.4.2":
  version: 4.5.0
  resolution: "webpack-bundle-analyzer@npm:4.5.0"
  dependencies:
    acorn: ^8.0.4
    acorn-walk: ^8.0.0
    chalk: ^4.1.0
    commander: ^7.2.0
    gzip-size: ^6.0.0
    lodash: ^4.17.20
    opener: ^1.5.2
    sirv: ^1.0.7
    ws: ^7.3.1
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: 158e96810ec213d5665ca1c0b257097db44e1f11c4befefab8352b9e5b10890fcb3e3fc1f7bb400dd58762a8edce5621c92afeca86eb4687d2eb64e93186bfcb
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^5.3.0":
  version: 5.3.0
  resolution: "webpack-dev-middleware@npm:5.3.0"
  dependencies:
    colorette: ^2.0.10
    memfs: ^3.2.2
    mime-types: ^2.1.31
    range-parser: ^1.2.1
    schema-utils: ^4.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 01f9e11583bb682cd5ab5a1b9d6dc99545f777513c4c15aa67d10f5d057fc3d0c6f9365e02c07792d3c9b17bd47a16c8185e66eb66e9de74d8ccf561e75085e7
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^4.5.0":
  version: 4.7.3
  resolution: "webpack-dev-server@npm:4.7.3"
  dependencies:
    "@types/bonjour": ^3.5.9
    "@types/connect-history-api-fallback": ^1.3.5
    "@types/serve-index": ^1.9.1
    "@types/sockjs": ^0.3.33
    "@types/ws": ^8.2.2
    ansi-html-community: ^0.0.8
    bonjour: ^3.5.0
    chokidar: ^3.5.2
    colorette: ^2.0.10
    compression: ^1.7.4
    connect-history-api-fallback: ^1.6.0
    default-gateway: ^6.0.3
    del: ^6.0.0
    express: ^4.17.1
    graceful-fs: ^4.2.6
    html-entities: ^2.3.2
    http-proxy-middleware: ^2.0.0
    ipaddr.js: ^2.0.1
    open: ^8.0.9
    p-retry: ^4.5.0
    portfinder: ^1.0.28
    schema-utils: ^4.0.0
    selfsigned: ^2.0.0
    serve-index: ^1.9.1
    sockjs: ^0.3.21
    spdy: ^4.0.2
    strip-ansi: ^7.0.0
    webpack-dev-middleware: ^5.3.0
    ws: ^8.1.0
  peerDependencies:
    webpack: ^4.37.0 || ^5.0.0
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: 6062db1ba62e372ab3bd127f0c1a575a0758ad15338bff56e65f344bfa495d566049752a3e0c18d44469123e2f0cd2ba26cc0cab8d4ca704e8b4ace596871b21
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3, webpack-merge@npm:^5.8.0":
  version: 5.8.0
  resolution: "webpack-merge@npm:5.8.0"
  dependencies:
    clone-deep: ^4.0.1
    wildcard: ^2.0.0
  checksum: 88786ab91013f1bd2a683834ff381be81c245a4b0f63304a5103e90f6653f44dab496a0768287f8531761f8ad957d1f9f3ccb2cb55df0de1bd9ee343e079da26
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.1.0, webpack-sources@npm:^1.4.3":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: ^2.0.0
    source-map: ~0.6.1
  checksum: 37463dad8d08114930f4bc4882a9602941f07c9f0efa9b6bc78738cd936275b990a596d801ef450d022bb005b109b9f451dd087db2f3c9baf53e8e22cf388f79
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.2":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack@npm:^5.61.0":
  version: 5.66.0
  resolution: "webpack@npm:5.66.0"
  dependencies:
    "@types/eslint-scope": ^3.7.0
    "@types/estree": ^0.0.50
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/wasm-edit": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    acorn: ^8.4.1
    acorn-import-assertions: ^1.7.6
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.8.3
    es-module-lexer: ^0.9.0
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-better-errors: ^1.0.2
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.1.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.1.3
    watchpack: ^2.3.1
    webpack-sources: ^3.2.2
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 5a44664a840fd64c5383aa78847b205ae0b42b607f85ee1d6a568617a210c1b9caab1822bce40a89a3b5eb0f626a83fb5fe2055c638aa230897bf224030d28e8
  languageName: node
  linkType: hard

"webpackbar@npm:^5.0.0-3":
  version: 5.0.2
  resolution: "webpackbar@npm:5.0.2"
  dependencies:
    chalk: ^4.1.0
    consola: ^2.15.3
    pretty-time: ^1.1.0
    std-env: ^3.0.1
  peerDependencies:
    webpack: 3 || 4 || 5
  checksum: 214a734b1d4d391eb8271ed1b11085f0efe6831e93f641229b292abfd6fea871422dce121612511c17ae8047522be6d65c1a2666cabb396c79549816a3612338
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: ">=0.5.1"
    safe-buffer: ">=5.1.0"
    websocket-extensions: ">=0.1.1"
  checksum: fffe5a33fe8eceafd21d2a065661d09e38b93877eae1de6ab5d7d2734c6ed243973beae10ae48c6613cfd675f200e5a058d1e3531bc9e6c5d4f1396ff1f0bfb9
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 5976835e68a86afcd64c7a9762ed85f2f27d48c488c707e67ba85e717b90fa066b98ab33c744d64255c9622d349eedecf728e65a5f921da71b58d0e9591b9038
  languageName: node
  linkType: hard

"whatwg-url@npm:^7.0.0":
  version: 7.1.0
  resolution: "whatwg-url@npm:7.1.0"
  dependencies:
    lodash.sortby: ^4.7.0
    tr46: ^1.0.1
    webidl-conversions: ^4.0.2
  checksum: fecb07c87290b47d2ec2fb6d6ca26daad3c9e211e0e531dd7566e7ff95b5b3525a57d4f32640ad4adf057717e0c215731db842ad761e61d947e81010e05cf5fd
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which@npm:^1.3.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.2":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: ^4.0.0
  checksum: 03db6c9d0af9329c37d74378ff1d91972b12553c7d72a6f4e8525fe61563fa7adb0b9d6e8d546b7e059688712ea874edd5ded475999abdeedf708de9849310e0
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.0
  resolution: "wildcard@npm:2.0.0"
  checksum: 1f4fe4c03dfc492777c60f795bbba597ac78794f1b650d68f398fbee9adb765367c516ebd4220889b6a81e9626e7228bbe0d66237abb311573c2ee1f4902a5ad
  languageName: node
  linkType: hard

"workbox-background-sync@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-background-sync@npm:6.4.2"
  dependencies:
    idb: ^6.1.4
    workbox-core: 6.4.2
  checksum: db8c267cef752176ab34b9d863334a700f27b70daa8109ca65fade7e2ff07f7969ccc2f64c075f043e2d8e3f89787c7f46e1bcde4c8a1a682f107c36f7e75d5e
  languageName: node
  linkType: hard

"workbox-broadcast-update@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-broadcast-update@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: cbf948c84530edce754797e205ed36a2b9db3b4a2d9a97d23cab56d84bcb880f5a9f0b22549e456199c52d2feee926a138a6b4a3982e820b4a31ed64dcdd5b7d
  languageName: node
  linkType: hard

"workbox-build@npm:^6.1.1":
  version: 6.4.2
  resolution: "workbox-build@npm:6.4.2"
  dependencies:
    "@apideck/better-ajv-errors": ^0.3.1
    "@babel/core": ^7.11.1
    "@babel/preset-env": ^7.11.0
    "@babel/runtime": ^7.11.2
    "@rollup/plugin-babel": ^5.2.0
    "@rollup/plugin-node-resolve": ^11.2.1
    "@rollup/plugin-replace": ^2.4.1
    "@surma/rollup-plugin-off-main-thread": ^2.2.3
    ajv: ^8.6.0
    common-tags: ^1.8.0
    fast-json-stable-stringify: ^2.1.0
    fs-extra: ^9.0.1
    glob: ^7.1.6
    lodash: ^4.17.20
    pretty-bytes: ^5.3.0
    rollup: ^2.43.1
    rollup-plugin-terser: ^7.0.0
    source-map: ^0.8.0-beta.0
    source-map-url: ^0.4.0
    stringify-object: ^3.3.0
    strip-comments: ^2.0.1
    tempy: ^0.6.0
    upath: ^1.2.0
    workbox-background-sync: 6.4.2
    workbox-broadcast-update: 6.4.2
    workbox-cacheable-response: 6.4.2
    workbox-core: 6.4.2
    workbox-expiration: 6.4.2
    workbox-google-analytics: 6.4.2
    workbox-navigation-preload: 6.4.2
    workbox-precaching: 6.4.2
    workbox-range-requests: 6.4.2
    workbox-recipes: 6.4.2
    workbox-routing: 6.4.2
    workbox-strategies: 6.4.2
    workbox-streams: 6.4.2
    workbox-sw: 6.4.2
    workbox-window: 6.4.2
  checksum: 3c8d45899b11420ae2584ce39487bd4a754e7a95bd79131ef7f3b7cbdbd6482048ef178fbb741182f45bcb4e0e9d43bcf3d2600347ea5a167ca396a0ffdce2b8
  languageName: node
  linkType: hard

"workbox-cacheable-response@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-cacheable-response@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: ca8e1d64ec55b9be8a79cd6b5d905a963693a13d9fd4641ac529e2bd88c03b3a7429b16252cd15e7f30351a90737a4095d6c896ef4e0aafdf652426a741cebbb
  languageName: node
  linkType: hard

"workbox-core@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-core@npm:6.4.2"
  checksum: bbdf4346e85d775d7162a49710957083bfa2b8cfc50b475bce02fcb62879ef1619ff381b00c969553a48b0c64c8b5ef7d9fce23fd5a64df1df8ed8f78667f23a
  languageName: node
  linkType: hard

"workbox-expiration@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-expiration@npm:6.4.2"
  dependencies:
    idb: ^6.1.4
    workbox-core: 6.4.2
  checksum: 15234417ec60af7fc6222bbf812619a35e2c4b62187f7c3777b2ebab28cf0c4de1d5e728bb380400eaa5a4f6263436b4889ba3b3fbc80bba05844094fb691316
  languageName: node
  linkType: hard

"workbox-google-analytics@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-google-analytics@npm:6.4.2"
  dependencies:
    workbox-background-sync: 6.4.2
    workbox-core: 6.4.2
    workbox-routing: 6.4.2
    workbox-strategies: 6.4.2
  checksum: 69e43a18c69881b293054af3550b38b182599ae93f261d5313f4a82a20b2c0f79667cf721ee9bf32cc76b1e2e77bd8409e5c8af02c7272f4553c7a1bc727b9f4
  languageName: node
  linkType: hard

"workbox-navigation-preload@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-navigation-preload@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: ab8433b12d7273057389b9ef36a8cae605ce713625a523925c14d3345be04abfa432d01206fd5f10295250e935c51a65e0284e13d99c128f0cbd22b040252358
  languageName: node
  linkType: hard

"workbox-precaching@npm:6.4.2, workbox-precaching@npm:^6.1.1":
  version: 6.4.2
  resolution: "workbox-precaching@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
    workbox-routing: 6.4.2
    workbox-strategies: 6.4.2
  checksum: b1d6c6a62418b4234b5a13aa1ed643908449ed1bc4acdbc2ffcc235341c36cd6e7b4d5fcee041c833b0c4bba07413a4da3a3a505b6f04745d2c19407e84e2f82
  languageName: node
  linkType: hard

"workbox-range-requests@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-range-requests@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: 940297ed423ac414b7edf59cf4e499230f8340713a4818de4a103296f2a1b29a52371f5f2e7bc3c41f3ea9317f974b80385e4cc58d2adeed6efc4ada251e14c0
  languageName: node
  linkType: hard

"workbox-recipes@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-recipes@npm:6.4.2"
  dependencies:
    workbox-cacheable-response: 6.4.2
    workbox-core: 6.4.2
    workbox-expiration: 6.4.2
    workbox-precaching: 6.4.2
    workbox-routing: 6.4.2
    workbox-strategies: 6.4.2
  checksum: 75a07ba6317f5e2fbf51b4a432914065fa8e62d232515664fc40eddc96a2c355ed03efb72411d1e73e947d40a845a2bad85c22c80e43e23fcb60b739f7869e31
  languageName: node
  linkType: hard

"workbox-routing@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-routing@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: 7cb503caa2c87572235b0b891a07bd9bcebd644bd8eec715982b4b5285867bf885e772feef0c2b6797c868e4d65b6d1014654afde0ba779177d683f7b44e23ac
  languageName: node
  linkType: hard

"workbox-strategies@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-strategies@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
  checksum: f981ab0bb103695f765cb4305d0bf35ebe347bcb19c441a58b2b48e99dc238495b6cbb1d1d3f55c89f2dee3202d9d2f8cb31f10b98120a33ed52f7e838366a98
  languageName: node
  linkType: hard

"workbox-streams@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-streams@npm:6.4.2"
  dependencies:
    workbox-core: 6.4.2
    workbox-routing: 6.4.2
  checksum: b17223f0a6604a869b6564ce29932146c4d8d8ec0e9f8d36cede5776ccde78fe0400598373c119209429ee01281d3e371c16e2722ae5d95dd67d8d526048ca14
  languageName: node
  linkType: hard

"workbox-sw@npm:6.4.2":
  version: 6.4.2
  resolution: "workbox-sw@npm:6.4.2"
  checksum: c9b05dc9af1f3da1cdb1ca8a2e57d273e63c76eaf29a216669234dea6934ee547f47836dd930143f3c04b5c756b38d1aa221efdc90f82bb69287bf3664849853
  languageName: node
  linkType: hard

"workbox-window@npm:6.4.2, workbox-window@npm:^6.1.1":
  version: 6.4.2
  resolution: "workbox-window@npm:6.4.2"
  dependencies:
    "@types/trusted-types": ^2.0.2
    workbox-core: 6.4.2
  checksum: 811dd5cae8f493e66e39729440b36a96ca3cb91b99595fd62f151c6f92f5e658109b0444aa3b91fafd1232220798c61c2164f1f2c76e21079abed9ceebe93f22
  languageName: node
  linkType: hard

"wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^3.0.0":
  version: 3.0.3
  resolution: "write-file-atomic@npm:3.0.3"
  dependencies:
    imurmurhash: ^0.1.4
    is-typedarray: ^1.0.0
    signal-exit: ^3.0.2
    typedarray-to-buffer: ^3.1.5
  checksum: c55b24617cc61c3a4379f425fc62a386cc51916a9b9d993f39734d005a09d5a4bb748bc251f1304e7abd71d0a26d339996c275955f527a131b1dcded67878280
  languageName: node
  linkType: hard

"ws@npm:^7.3.1":
  version: 7.5.6
  resolution: "ws@npm:7.5.6"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 0c2ffc9a539dd61dd2b00ff6cc5c98a3371e2521011fe23da4b3578bb7ac26cbdf7ca8a68e8e08023c122ae247013216dde2a20c908de415a6bcc87bdef68c87
  languageName: node
  linkType: hard

"ws@npm:^8.1.0":
  version: 8.4.0
  resolution: "ws@npm:8.4.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 5e37ccf0ecb8d8019d88b07af079e8f74248b688ad3109ab57cd5e1c9a7392545f572914d0c27f25e8b83a6cfc09a89ac151c556ff4fae26d6f824077e4f8239
  languageName: node
  linkType: hard

"xdg-basedir@npm:^4.0.0":
  version: 4.0.0
  resolution: "xdg-basedir@npm:4.0.0"
  checksum: 0073d5b59a37224ed3a5ac0dd2ec1d36f09c49f0afd769008a6e9cd3cd666bd6317bd1c7ce2eab47e1de285a286bad11a9b038196413cd753b79770361855f3c
  languageName: node
  linkType: hard

"xml-js@npm:^1.6.11":
  version: 1.6.11
  resolution: "xml-js@npm:1.6.11"
  dependencies:
    sax: ^1.2.4
  bin:
    xml-js: ./bin/cli.js
  checksum: 24a55479919413687105fc2d8ab05e613ebedb1c1bc12258a108e07cff5ef793779297db854800a4edf0281303ebd1f177bc4a588442f5344e62b3dddda26c2b
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:^4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0, yaml@npm:^1.10.2, yaml@npm:^1.7.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zwitch@npm:^1.0.0":
  version: 1.0.5
  resolution: "zwitch@npm:1.0.5"
  checksum: 28a1bebacab3bc60150b6b0a2ba1db2ad033f068e81f05e4892ec0ea13ae63f5d140a1d692062ac0657840c8da076f35b94433b5f1c329d7803b247de80f064a
  languageName: node
  linkType: hard
